/**
 * Chat Configuration Component
 * Advanced configuration panel for GLM-4.5 features
 */

import React, { useState, useEffect } from 'react';
import { 
  CogIcon, 
  BoltIcon, 
  DocumentTextIcon, 
  WrenchScrewdriverIcon,
  CloudIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import { GLMModelConfig, SubscriptionTier } from '@/types/glm-models';
import { modelRegistry } from '@/config/glm-model-registry';
import { subscriptionService } from '@/services/subscriptionService';

interface ChatConfigurationProps {
  selectedModelId?: string;
  userId?: string;
  onConfigChange: (config: ChatConfig) => void;
  className?: string;
}

export interface ChatConfig {
  temperature: number;
  maxTokens: number;
  topP: number;
  thinking: {
    enabled: boolean;
    depth: 'basic' | 'advanced' | 'deep';
    budgetTokens: number;
  };
  structuredOutput: {
    enabled: boolean;
    format: 'json' | 'yaml';
    schema?: any;
  };
  functionCalling: {
    enabled: boolean;
    tools: any[];
  };
  contextCaching: {
    enabled: boolean;
    ttlHours: number;
    useCompression: boolean;
  };
  streaming: boolean;
}

const defaultConfig: ChatConfig = {
  temperature: 0.7,
  maxTokens: 4000,
  topP: 0.9,
  thinking: {
    enabled: false,
    depth: 'basic',
    budgetTokens: 1000
  },
  structuredOutput: {
    enabled: false,
    format: 'json'
  },
  functionCalling: {
    enabled: false,
    tools: []
  },
  contextCaching: {
    enabled: true,
    ttlHours: 24,
    useCompression: true
  },
  streaming: true
};

export const ChatConfiguration: React.FC<ChatConfigurationProps> = ({
  selectedModelId,
  userId,
  onConfigChange,
  className = ''
}) => {
  const [config, setConfig] = useState<ChatConfig>(defaultConfig);
  const [modelConfig, setModelConfig] = useState<GLMModelConfig | null>(null);
  const [userTier, setUserTier] = useState<SubscriptionTier>('free');
  const [isExpanded, setIsExpanded] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadModelAndUserData();
  }, [selectedModelId, userId]);

  useEffect(() => {
    onConfigChange(config);
  }, [config, onConfigChange]);

  const loadModelAndUserData = async () => {
    try {
      setLoading(true);
      
      // Get model configuration
      if (selectedModelId) {
        const model = modelRegistry.getModel(selectedModelId);
        setModelConfig(model);
        
        // Update config limits based on model capabilities
        if (model) {
          setConfig(prev => ({
            ...prev,
            maxTokens: Math.min(prev.maxTokens, model.capabilities.maxTokens),
            thinking: {
              ...prev.thinking,
              enabled: prev.thinking.enabled && model.capabilities.supportsThinking
            },
            structuredOutput: {
              ...prev.structuredOutput,
              enabled: prev.structuredOutput.enabled && model.capabilities.supportsStructuredOutput
            },
            functionCalling: {
              ...prev.functionCalling,
              enabled: prev.functionCalling.enabled && model.capabilities.supportsFunctionCalling
            },
            streaming: prev.streaming && model.capabilities.supportsStreaming
          }));
        }
      }

      // Get user tier
      if (userId) {
        const tier = await subscriptionService.getUserTier(userId);
        setUserTier(tier);
      }
    } catch (error) {
      console.error('Failed to load model and user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = (updates: Partial<ChatConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const updateNestedConfig = <T extends keyof ChatConfig>(
    section: T,
    updates: Partial<ChatConfig[T]>
  ) => {
    setConfig(prev => ({
      ...prev,
      [section]: { ...prev[section], ...updates }
    }));
  };

  const isFeatureAvailable = (feature: string): boolean => {
    if (!modelConfig) return false;
    
    switch (feature) {
      case 'thinking':
        return modelConfig.capabilities.supportsThinking && userTier !== 'free';
      case 'structuredOutput':
        return modelConfig.capabilities.supportsStructuredOutput && userTier !== 'free';
      case 'functionCalling':
        return modelConfig.capabilities.supportsFunctionCalling && userTier !== 'free';
      case 'contextCaching':
        return userTier !== 'free';
      case 'advancedThinking':
        return modelConfig.capabilities.supportsThinking && userTier === 'premium';
      default:
        return true;
    }
  };

  if (loading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          <div className="h-8 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2">
          <CogIcon className="h-5 w-5 text-gray-500" />
          <span className="font-medium text-gray-900">Chat Configuration</span>
          {modelConfig && (
            <span className="text-sm text-gray-500">({modelConfig.displayName})</span>
          )}
        </div>
        <AdjustmentsHorizontalIcon 
          className={`h-5 w-5 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
        />
      </div>

      {isExpanded && (
        <div className="border-t border-gray-200 p-4 space-y-6">
          {/* Basic Parameters */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Basic Parameters</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Temperature
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={config.temperature}
                  onChange={(e) => updateConfig({ temperature: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 mt-1">{config.temperature}</div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Tokens
                </label>
                <input
                  type="number"
                  min="1"
                  max={modelConfig?.capabilities.maxTokens || 8192}
                  value={config.maxTokens}
                  onChange={(e) => updateConfig({ maxTokens: parseInt(e.target.value) })}
                  className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Top P
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.05"
                  value={config.topP}
                  onChange={(e) => updateConfig({ topP: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 mt-1">{config.topP}</div>
              </div>
            </div>
          </div>

          {/* Thinking Mode */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <BoltIcon className="h-4 w-4 text-yellow-500" />
                <span className="font-medium text-gray-900">Thinking Mode</span>
                {!isFeatureAvailable('thinking') && (
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {userTier === 'free' ? 'Upgrade Required' : 'Not Supported'}
                  </span>
                )}
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={config.thinking.enabled}
                  onChange={(e) => updateNestedConfig('thinking', { enabled: e.target.checked })}
                  disabled={!isFeatureAvailable('thinking')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {config.thinking.enabled && isFeatureAvailable('thinking') && (
              <div className="ml-6 space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Thinking Depth
                  </label>
                  <select
                    value={config.thinking.depth}
                    onChange={(e) => updateNestedConfig('thinking', { depth: e.target.value as any })}
                    disabled={!isFeatureAvailable('advancedThinking') && config.thinking.depth !== 'basic'}
                    className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="basic">Basic</option>
                    <option value="advanced" disabled={!isFeatureAvailable('advancedThinking')}>
                      Advanced {!isFeatureAvailable('advancedThinking') && '(Premium)'}
                    </option>
                    <option value="deep" disabled={!isFeatureAvailable('advancedThinking')}>
                      Deep {!isFeatureAvailable('advancedThinking') && '(Premium)'}
                    </option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Budget Tokens
                  </label>
                  <input
                    type="number"
                    min="100"
                    max="5000"
                    value={config.thinking.budgetTokens}
                    onChange={(e) => updateNestedConfig('thinking', { budgetTokens: parseInt(e.target.value) })}
                    className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Structured Output */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <DocumentTextIcon className="h-4 w-4 text-green-500" />
                <span className="font-medium text-gray-900">Structured Output</span>
                {!isFeatureAvailable('structuredOutput') && (
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {userTier === 'free' ? 'Upgrade Required' : 'Not Supported'}
                  </span>
                )}
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={config.structuredOutput.enabled}
                  onChange={(e) => updateNestedConfig('structuredOutput', { enabled: e.target.checked })}
                  disabled={!isFeatureAvailable('structuredOutput')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {config.structuredOutput.enabled && isFeatureAvailable('structuredOutput') && (
              <div className="ml-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Output Format
                </label>
                <select
                  value={config.structuredOutput.format}
                  onChange={(e) => updateNestedConfig('structuredOutput', { format: e.target.value as any })}
                  className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value="json">JSON</option>
                  <option value="yaml">YAML</option>
                </select>
              </div>
            )}
          </div>

          {/* Context Caching */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CloudIcon className="h-4 w-4 text-blue-500" />
                <span className="font-medium text-gray-900">Context Caching</span>
                {!isFeatureAvailable('contextCaching') && (
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    Upgrade Required
                  </span>
                )}
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={config.contextCaching.enabled}
                  onChange={(e) => updateNestedConfig('contextCaching', { enabled: e.target.checked })}
                  disabled={!isFeatureAvailable('contextCaching')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {config.contextCaching.enabled && isFeatureAvailable('contextCaching') && (
              <div className="ml-6 grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    TTL (Hours)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="168"
                    value={config.contextCaching.ttlHours}
                    onChange={(e) => updateNestedConfig('contextCaching', { ttlHours: parseInt(e.target.value) })}
                    className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                  />
                </div>

                <div className="flex items-center">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={config.contextCaching.useCompression}
                      onChange={(e) => updateNestedConfig('contextCaching', { useCompression: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Use Compression</span>
                  </label>
                </div>
              </div>
            )}
          </div>

          {/* Streaming */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BoltIcon className="h-4 w-4 text-purple-500" />
              <span className="font-medium text-gray-900">Streaming Response</span>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.streaming}
                onChange={(e) => updateConfig({ streaming: e.target.checked })}
                disabled={!modelConfig?.capabilities.supportsStreaming}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatConfiguration;
