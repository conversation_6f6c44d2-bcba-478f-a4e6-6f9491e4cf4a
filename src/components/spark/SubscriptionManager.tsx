/**
 * Subscription Manager Component
 * Manages user subscriptions and displays usage analytics
 */

import React, { useState, useEffect } from 'react';
import { 
  CreditCardIcon, 
  ChartBarIcon, 
  StarIcon, 
  CheckIcon,
  XMarkIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';
import { SubscriptionTier } from '@/types/glm-models';
import { subscriptionService } from '@/services/subscriptionService';
import { modelUsageService } from '@/services/modelUsageService';

interface SubscriptionManagerProps {
  userId: string;
  onUpgrade?: (tier: SubscriptionTier) => void;
  className?: string;
}

interface UsageStats {
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  modelBreakdown: Array<{
    modelId: string;
    requests: number;
    tokens: number;
    cost: number;
  }>;
}

const tierFeatures = {
  free: {
    name: 'Free',
    price: '$0',
    models: ['GLM-4.5-Flash'],
    features: [
      '100 requests/month',
      'Basic model access',
      'Standard response time',
      'Community support'
    ],
    limitations: [
      'No thinking mode',
      'No structured output',
      'No context caching',
      'Limited model selection'
    ]
  },
  basic: {
    name: 'Basic',
    price: '$9.99',
    models: ['GLM-4.5-Flash', 'GLM-4.5-Air'],
    features: [
      '1,000 requests/month',
      'Enhanced models',
      'Thinking mode (basic)',
      'Context caching',
      'Email support'
    ],
    limitations: [
      'No premium models',
      'Limited thinking depth',
      'Basic analytics'
    ]
  },
  premium: {
    name: 'Premium',
    price: '$29.99',
    models: ['All GLM-4.5 variants'],
    features: [
      'Unlimited requests',
      'All model access',
      'Advanced thinking mode',
      'Structured output',
      'Function calling',
      'Priority support',
      'Advanced analytics',
      'Custom configurations'
    ],
    limitations: []
  }
};

export const SubscriptionManager: React.FC<SubscriptionManagerProps> = ({
  userId,
  onUpgrade,
  className = ''
}) => {
  const [currentTier, setCurrentTier] = useState<SubscriptionTier>('free');
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  useEffect(() => {
    loadSubscriptionData();
  }, [userId]);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      
      // Get current subscription tier
      const tier = await subscriptionService.getUserTier(userId);
      setCurrentTier(tier);

      // Get usage statistics
      const stats = await modelUsageService.getUserUsageStats(userId, {
        period: 'current_month'
      });
      
      setUsageStats({
        totalRequests: stats.totalRequests || 0,
        totalTokens: stats.totalTokens || 0,
        totalCost: stats.totalCost || 0,
        modelBreakdown: stats.modelBreakdown || []
      });
    } catch (error) {
      console.error('Failed to load subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (targetTier: SubscriptionTier) => {
    try {
      // In a real app, this would integrate with Stripe
      console.log(`Upgrading to ${targetTier}`);
      onUpgrade?.(targetTier);
      setShowUpgradeModal(false);
    } catch (error) {
      console.error('Upgrade failed:', error);
    }
  };

  const getTierColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case 'premium':
        return 'text-purple-600 bg-purple-100 border-purple-200';
      case 'basic':
        return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'free':
        return 'text-gray-600 bg-gray-100 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Current Subscription */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <CreditCardIcon className="h-5 w-5 text-blue-500" />
            Current Subscription
          </h3>
          <span className={`
            inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border
            ${getTierColor(currentTier)}
          `}>
            <StarIcon className="h-4 w-4 mr-1" />
            {tierFeatures[currentTier].name}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{usageStats?.totalRequests || 0}</div>
            <div className="text-sm text-gray-600">Requests This Month</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {usageStats?.totalTokens?.toLocaleString() || 0}
            </div>
            <div className="text-sm text-gray-600">Tokens Used</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              ${usageStats?.totalCost?.toFixed(2) || '0.00'}
            </div>
            <div className="text-sm text-gray-600">Total Cost</div>
          </div>
        </div>

        {currentTier !== 'premium' && (
          <button
            onClick={() => setShowUpgradeModal(true)}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
          >
            <ArrowUpIcon className="h-4 w-4" />
            Upgrade Subscription
          </button>
        )}
      </div>

      {/* Usage Analytics */}
      <div className="p-6">
        <h4 className="text-md font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <ChartBarIcon className="h-5 w-5 text-green-500" />
          Usage Analytics
        </h4>

        {usageStats?.modelBreakdown && usageStats.modelBreakdown.length > 0 ? (
          <div className="space-y-3">
            {usageStats.modelBreakdown.map((model) => (
              <div key={model.modelId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{model.modelId}</div>
                  <div className="text-sm text-gray-600">
                    {model.requests} requests • {model.tokens.toLocaleString()} tokens
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-gray-900">${model.cost.toFixed(2)}</div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No usage data available for this month
          </div>
        )}
      </div>

      {/* Upgrade Modal */}
      {showUpgradeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900">Choose Your Plan</h3>
                <button
                  onClick={() => setShowUpgradeModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {Object.entries(tierFeatures).map(([tier, features]) => (
                  <div
                    key={tier}
                    className={`
                      border rounded-lg p-6 relative
                      ${currentTier === tier ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}
                      ${tier === 'premium' ? 'ring-2 ring-purple-500' : ''}
                    `}
                  >
                    {tier === 'premium' && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}

                    <div className="text-center mb-4">
                      <h4 className="text-xl font-semibold text-gray-900">{features.name}</h4>
                      <div className="text-3xl font-bold text-gray-900 mt-2">
                        {features.price}
                        <span className="text-sm font-normal text-gray-600">/month</span>
                      </div>
                    </div>

                    <div className="space-y-3 mb-6">
                      <div>
                        <div className="text-sm font-medium text-gray-900 mb-2">Features:</div>
                        {features.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm text-gray-600">
                            <CheckIcon className="h-4 w-4 text-green-500 flex-shrink-0" />
                            {feature}
                          </div>
                        ))}
                      </div>

                      {features.limitations.length > 0 && (
                        <div>
                          <div className="text-sm font-medium text-gray-900 mb-2">Limitations:</div>
                          {features.limitations.map((limitation, index) => (
                            <div key={index} className="flex items-center gap-2 text-sm text-gray-500">
                              <XMarkIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                              {limitation}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <button
                      onClick={() => handleUpgrade(tier as SubscriptionTier)}
                      disabled={currentTier === tier}
                      className={`
                        w-full py-2 px-4 rounded-lg font-medium transition-colors
                        ${currentTier === tier
                          ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                          : tier === 'premium'
                          ? 'bg-purple-600 text-white hover:bg-purple-700'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                        }
                      `}
                    >
                      {currentTier === tier ? 'Current Plan' : `Upgrade to ${features.name}`}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionManager;
