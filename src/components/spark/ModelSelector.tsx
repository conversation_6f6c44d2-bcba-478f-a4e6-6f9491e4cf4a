/**
 * Model Selector Component
 * Allows users to select from available GLM-4.5 models based on their subscription tier
 */

import React, { useState, useEffect } from 'react';
import { ChevronDownIcon, CheckIcon, LockClosedIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { GLMModelConfig, SubscriptionTier } from '@/types/glm-models';
import { modelRegistry } from '@/config/glm-model-registry';
import { subscriptionService } from '@/services/subscriptionService';

interface ModelSelectorProps {
  selectedModelId?: string;
  onModelSelect: (modelId: string) => void;
  userId?: string;
  disabled?: boolean;
  showUpgradePrompt?: boolean;
  className?: string;
}

interface ModelOption extends GLMModelConfig {
  available: boolean;
  upgradeRequired: boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModelId,
  onModelSelect,
  userId,
  disabled = false,
  showUpgradePrompt = true,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [models, setModels] = useState<ModelOption[]>([]);
  const [userTier, setUserTier] = useState<SubscriptionTier>('free');
  const [loading, setLoading] = useState(true);
  const [selectedModel, setSelectedModel] = useState<ModelOption | null>(null);

  useEffect(() => {
    loadAvailableModels();
  }, [userId]);

  useEffect(() => {
    if (selectedModelId && models.length > 0) {
      const model = models.find(m => m.id === selectedModelId);
      setSelectedModel(model || null);
    }
  }, [selectedModelId, models]);

  const loadAvailableModels = async () => {
    try {
      setLoading(true);
      
      // Get user's subscription tier
      const tier = userId 
        ? await subscriptionService.getUserTier(userId)
        : 'free';
      setUserTier(tier);

      // Get all models and check availability
      const allModels = modelRegistry.getAllModels();
      const modelOptions: ModelOption[] = await Promise.all(
        allModels.map(async (model) => {
          const available = userId 
            ? await subscriptionService.validateModelAccess(userId, model.id)
            : modelRegistry.validateModelAccess(model.id, tier);
          
          return {
            ...model,
            available,
            upgradeRequired: !available && modelRegistry.isModelAccessibleForTier(model, 'premium')
          };
        })
      );

      // Sort models: available first, then by quality score
      modelOptions.sort((a, b) => {
        if (a.available !== b.available) {
          return a.available ? -1 : 1;
        }
        return b.capabilities.qualityScore - a.capabilities.qualityScore;
      });

      setModels(modelOptions);

      // Auto-select default model if none selected
      if (!selectedModelId) {
        const defaultModel = userId
          ? await subscriptionService.getDefaultModel(userId)
          : modelRegistry.getDefaultModel(tier);
        
        if (defaultModel) {
          const defaultOption = modelOptions.find(m => m.id === defaultModel.id);
          if (defaultOption?.available) {
            setSelectedModel(defaultOption);
            onModelSelect(defaultModel.id);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load available models:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleModelSelect = (model: ModelOption) => {
    if (!model.available || disabled) return;
    
    setSelectedModel(model);
    onModelSelect(model.id);
    setIsOpen(false);
  };

  const getModelSpeedIcon = (speed: string) => {
    switch (speed) {
      case 'ultra-fast':
        return '⚡⚡⚡';
      case 'fast':
        return '⚡⚡';
      case 'standard':
        return '⚡';
      default:
        return '';
    }
  };

  const getTierBadgeColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case 'premium':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'basic':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'free':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 animate-pulse">
          <div className="h-5 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          relative w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm
          focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500
          ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'cursor-pointer hover:bg-gray-50'}
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {selectedModel ? (
              <>
                <SparklesIcon className="h-4 w-4 text-blue-500" />
                <span className="font-medium">{selectedModel.displayName}</span>
                <span className="text-sm text-gray-500">
                  {getModelSpeedIcon(selectedModel.capabilities.speed)}
                </span>
                <span className={`
                  inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border
                  ${getTierBadgeColor(selectedModel.tier)}
                `}>
                  {selectedModel.tier}
                </span>
              </>
            ) : (
              <span className="text-gray-500">Select a model...</span>
            )}
          </div>
          <ChevronDownIcon 
            className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          />
        </div>
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-auto">
          <div className="py-1">
            {models.map((model) => (
              <div
                key={model.id}
                onClick={() => handleModelSelect(model)}
                className={`
                  relative px-3 py-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 last:border-b-0
                  ${!model.available ? 'opacity-60 cursor-not-allowed' : ''}
                  ${selectedModel?.id === model.id ? 'bg-blue-50' : ''}
                `}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-gray-900">{model.displayName}</span>
                      <span className="text-sm text-gray-500">
                        {getModelSpeedIcon(model.capabilities.speed)}
                      </span>
                      <span className={`
                        inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border
                        ${getTierBadgeColor(model.tier)}
                      `}>
                        {model.tier}
                      </span>
                      {!model.available && (
                        <LockClosedIcon className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{model.description}</p>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Max: {model.capabilities.maxTokens.toLocaleString()} tokens</span>
                      <span>Context: {model.capabilities.contextLength.toLocaleString()}</span>
                      <span>Quality: {model.capabilities.qualityScore}/10</span>
                    </div>
                    
                    <div className="flex items-center space-x-2 mt-1">
                      {model.capabilities.supportsThinking && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-green-100 text-green-800">
                          Thinking
                        </span>
                      )}
                      {model.capabilities.supportsStructuredOutput && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                          Structured
                        </span>
                      )}
                      {model.capabilities.supportsFunctionCalling && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-purple-100 text-purple-800">
                          Functions
                        </span>
                      )}
                      {model.capabilities.supportsVision && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-orange-100 text-orange-800">
                          Vision
                        </span>
                      )}
                    </div>

                    {!model.available && model.upgradeRequired && showUpgradePrompt && (
                      <div className="mt-2 text-xs text-blue-600">
                        Upgrade to {model.tier} to access this model
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center ml-3">
                    {selectedModel?.id === model.id && model.available && (
                      <CheckIcon className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {showUpgradePrompt && models.some(m => !m.available) && (
            <div className="px-3 py-2 bg-gray-50 border-t border-gray-200">
              <div className="text-xs text-gray-600 text-center">
                <span>Upgrade your subscription to access more powerful models</span>
                <button className="ml-2 text-blue-600 hover:text-blue-800 font-medium">
                  View Plans
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ModelSelector;
