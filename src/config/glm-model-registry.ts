/**
 * GLM-4.5 Model Registry
 * Central configuration for all GLM-4.5 model variants
 */

import {
  GLMModelConfig,
  ModelRegistry,
  SubscriptionTier,
  GLM_MODEL_IDS,
  SUBSCRIPTION_TIERS,
  MODEL_SPEEDS,
  ModelConfigurationError
} from '../types/glm-models';

// Model Configurations
const GLM_MODELS: Record<string, GLMModelConfig> = {
  [GLM_MODEL_IDS.GLM_4_5]: {
    id: GLM_MODEL_IDS.GLM_4_5,
    name: 'glm-4.5',
    displayName: 'GLM-4.5',
    description: 'Most capable model with highest quality reasoning and comprehensive capabilities',
    tier: SUBSCRIPTION_TIERS.PREMIUM,
    pricing: {
      inputTokens: 50,   // $50 per million input tokens
      outputTokens: 150, // $150 per million output tokens
      currency: 'USD'
    },
    capabilities: {
      maxTokens: 8192,
      contextLength: 128000,
      supportsThinking: true,
      supportsStreaming: true,
      supportsFunctionCalling: true,
      supportsStructuredOutput: true,
      supportsVision: true,
      speed: MODEL_SPEEDS.STANDARD,
      qualityScore: 10
    },
    limits: {
      requestsPerMinute: 60,
      requestsPerDay: 10000,
      tokensPerRequest: 8192,
      concurrentRequests: 5
    },
    isActive: true,
    releaseDate: '2024-11-01'
  },

  [GLM_MODEL_IDS.GLM_4_5_AIR]: {
    id: GLM_MODEL_IDS.GLM_4_5_AIR,
    name: 'glm-4.5-air',
    displayName: 'GLM-4.5-Air',
    description: 'Balanced model optimized for speed and efficiency with good quality',
    tier: SUBSCRIPTION_TIERS.BASIC,
    pricing: {
      inputTokens: 10,   // $10 per million input tokens
      outputTokens: 30,  // $30 per million output tokens
      currency: 'USD'
    },
    capabilities: {
      maxTokens: 4096,
      contextLength: 32000,
      supportsThinking: true,
      supportsStreaming: true,
      supportsFunctionCalling: true,
      supportsStructuredOutput: true,
      supportsVision: false,
      speed: MODEL_SPEEDS.FAST,
      qualityScore: 8
    },
    limits: {
      requestsPerMinute: 120,
      requestsPerDay: 50000,
      tokensPerRequest: 4096,
      concurrentRequests: 10
    },
    isActive: true,
    releaseDate: '2024-11-01'
  },

  [GLM_MODEL_IDS.GLM_4_5_X]: {
    id: GLM_MODEL_IDS.GLM_4_5_X,
    name: 'glm-4.5-x',
    displayName: 'GLM-4.5-X',
    description: 'Extended context model for complex reasoning and long conversations',
    tier: SUBSCRIPTION_TIERS.PREMIUM,
    pricing: {
      inputTokens: 75,   // $75 per million input tokens
      outputTokens: 225, // $225 per million output tokens
      currency: 'USD'
    },
    capabilities: {
      maxTokens: 8192,
      contextLength: 1000000, // 1M context length
      supportsThinking: true,
      supportsStreaming: true,
      supportsFunctionCalling: true,
      supportsStructuredOutput: true,
      supportsVision: true,
      speed: MODEL_SPEEDS.STANDARD,
      qualityScore: 10
    },
    limits: {
      requestsPerMinute: 30,
      requestsPerDay: 5000,
      tokensPerRequest: 8192,
      concurrentRequests: 3
    },
    isActive: true,
    releaseDate: '2024-12-01'
  },

  [GLM_MODEL_IDS.GLM_4_5_AIRX]: {
    id: GLM_MODEL_IDS.GLM_4_5_AIRX,
    name: 'glm-4.5-airx',
    displayName: 'GLM-4.5-AirX',
    description: 'Extended context version of Air model for longer conversations',
    tier: SUBSCRIPTION_TIERS.BASIC,
    pricing: {
      inputTokens: 20,   // $20 per million input tokens
      outputTokens: 60,  // $60 per million output tokens
      currency: 'USD'
    },
    capabilities: {
      maxTokens: 4096,
      contextLength: 128000,
      supportsThinking: true,
      supportsStreaming: true,
      supportsFunctionCalling: true,
      supportsStructuredOutput: true,
      supportsVision: false,
      speed: MODEL_SPEEDS.FAST,
      qualityScore: 8
    },
    limits: {
      requestsPerMinute: 90,
      requestsPerDay: 25000,
      tokensPerRequest: 4096,
      concurrentRequests: 8
    },
    isActive: true,
    releaseDate: '2024-12-01'
  },

  [GLM_MODEL_IDS.GLM_4_5_FLASH]: {
    id: GLM_MODEL_IDS.GLM_4_5_FLASH,
    name: 'glm-4.5-flash',
    displayName: 'GLM-4.5-Flash',
    description: 'Ultra-fast model optimized for speed with basic capabilities',
    tier: SUBSCRIPTION_TIERS.FREE,
    pricing: {
      inputTokens: 2,    // $2 per million input tokens
      outputTokens: 6,   // $6 per million output tokens
      currency: 'USD'
    },
    capabilities: {
      maxTokens: 2048,
      contextLength: 8000,
      supportsThinking: false,
      supportsStreaming: true,
      supportsFunctionCalling: false,
      supportsStructuredOutput: false,
      supportsVision: false,
      speed: MODEL_SPEEDS.ULTRA_FAST,
      qualityScore: 6
    },
    limits: {
      requestsPerMinute: 20,
      requestsPerDay: 1000,
      tokensPerRequest: 2048,
      concurrentRequests: 2
    },
    isActive: true,
    releaseDate: '2024-11-01'
  }
};

// Model Registry Implementation
class GLMModelRegistry implements ModelRegistry {
  public readonly models = GLM_MODELS;

  getModel(id: string): GLMModelConfig | null {
    return this.models[id] || null;
  }

  getModelsByTier(tier: SubscriptionTier): GLMModelConfig[] {
    return Object.values(this.models).filter(model => 
      model.isActive && this.isModelAccessibleForTier(model, tier)
    );
  }

  getAvailableModels(tier: SubscriptionTier): GLMModelConfig[] {
    return this.getModelsByTier(tier).sort((a, b) => {
      // Sort by tier priority, then by quality score
      const tierPriority = { premium: 3, basic: 2, free: 1 };
      const aPriority = tierPriority[a.tier];
      const bPriority = tierPriority[b.tier];
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      return b.capabilities.qualityScore - a.capabilities.qualityScore;
    });
  }

  validateModelAccess(modelId: string, tier: SubscriptionTier): boolean {
    const model = this.getModel(modelId);
    if (!model || !model.isActive) {
      return false;
    }
    
    return this.isModelAccessibleForTier(model, tier);
  }

  getDefaultModel(tier: SubscriptionTier): GLMModelConfig | null {
    const availableModels = this.getAvailableModels(tier);
    
    // Return the best model available for the tier
    switch (tier) {
      case SUBSCRIPTION_TIERS.PREMIUM:
        return availableModels.find(m => m.id === GLM_MODEL_IDS.GLM_4_5) || availableModels[0] || null;
      case SUBSCRIPTION_TIERS.BASIC:
        return availableModels.find(m => m.id === GLM_MODEL_IDS.GLM_4_5_AIR) || availableModels[0] || null;
      case SUBSCRIPTION_TIERS.FREE:
        return availableModels.find(m => m.id === GLM_MODEL_IDS.GLM_4_5_FLASH) || availableModels[0] || null;
      default:
        return null;
    }
  }

  private isModelAccessibleForTier(model: GLMModelConfig, userTier: SubscriptionTier): boolean {
    const tierHierarchy = {
      [SUBSCRIPTION_TIERS.FREE]: 1,
      [SUBSCRIPTION_TIERS.BASIC]: 2,
      [SUBSCRIPTION_TIERS.PREMIUM]: 3
    };
    
    return tierHierarchy[userTier] >= tierHierarchy[model.tier];
  }

  // Utility methods
  getAllModels(): GLMModelConfig[] {
    return Object.values(this.models).filter(model => model.isActive);
  }

  getModelCapabilities(modelId: string): GLMModelConfig['capabilities'] | null {
    const model = this.getModel(modelId);
    return model?.capabilities || null;
  }

  getModelPricing(modelId: string): GLMModelConfig['pricing'] | null {
    const model = this.getModel(modelId);
    return model?.pricing || null;
  }

  getModelLimits(modelId: string): GLMModelConfig['limits'] | null {
    const model = this.getModel(modelId);
    return model?.limits || null;
  }

  calculateCost(modelId: string, inputTokens: number, outputTokens: number): number {
    const pricing = this.getModelPricing(modelId);
    if (!pricing) {
      throw new ModelConfigurationError(`Model ${modelId} not found`, modelId);
    }
    
    const inputCost = (inputTokens / 1_000_000) * pricing.inputTokens;
    const outputCost = (outputTokens / 1_000_000) * pricing.outputTokens;
    
    return inputCost + outputCost;
  }
}

// Export singleton instance
export const modelRegistry = new GLMModelRegistry();
export default modelRegistry;
