/**
 * GLM-4.5 Multi-Model Configuration Types
 * Defines interfaces for all GLM-4.5 model variants and their capabilities
 */

export type SubscriptionTier = 'free' | 'basic' | 'premium';
export type ModelSpeed = 'standard' | 'fast' | 'ultra-fast';
export type RequestType = 'chat' | 'thinking' | 'structured' | 'function_call';

export interface ModelCapabilities {
  maxTokens: number;
  contextLength: number;
  supportsThinking: boolean;
  supportsStreaming: boolean;
  supportsFunctionCalling: boolean;
  supportsStructuredOutput: boolean;
  supportsVision: boolean;
  speed: ModelSpeed;
  qualityScore: number; // 1-10 scale
}

export interface ModelPricing {
  inputTokens: number;  // Cost per million tokens
  outputTokens: number; // Cost per million tokens
  currency: string;     // 'USD', 'EUR', etc.
}

export interface ModelLimits {
  requestsPerMinute: number;
  requestsPerDay: number;
  tokensPerRequest: number;
  concurrentRequests: number;
}

export interface GLMModelConfig {
  id: string;
  name: string;
  displayName: string;
  description: string;
  tier: SubscriptionTier;
  pricing: ModelPricing;
  capabilities: ModelCapabilities;
  limits: ModelLimits;
  endpoint?: string;
  isActive: boolean;
  releaseDate: string;
  deprecationDate?: string;
}

export interface ModelRegistry {
  models: Record<string, GLMModelConfig>;
  getModel(id: string): GLMModelConfig | null;
  getModelsByTier(tier: SubscriptionTier): GLMModelConfig[];
  getAvailableModels(tier: SubscriptionTier): GLMModelConfig[];
  validateModelAccess(modelId: string, tier: SubscriptionTier): boolean;
  getDefaultModel(tier: SubscriptionTier): GLMModelConfig | null;
}

export interface ModelUsageMetrics {
  modelId: string;
  requestType: RequestType;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  cost: number;
  responseTime: number;
  success: boolean;
  errorCode?: string;
  errorMessage?: string;
  timestamp: Date;
}

export interface SubscriptionFeature {
  key: string;
  value: string;
  enabled: boolean;
}

export interface UserSubscription {
  id: string;
  userId: string;
  tier: SubscriptionTier;
  status: 'active' | 'inactive' | 'expired' | 'cancelled';
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  currentPeriodStart?: Date;
  currentPeriodEnd?: Date;
  cancelAtPeriodEnd: boolean;
  features: SubscriptionFeature[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ContextCacheEntry {
  id: string;
  conversationId: string;
  cacheKey: string;
  contextData: string;
  tokenCount: number;
  compressionRatio?: number;
  ttl: Date;
  hitCount: number;
  lastAccessed: Date;
  createdAt: Date;
  updatedAt: Date;
}

// API Request/Response Types
export interface GLMRequest {
  model: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  stream?: boolean;
  thinking?: boolean;
  tools?: any[];
  tool_choice?: any;
  response_format?: {
    type: 'json_object';
    schema?: any;
  };
}

export interface GLMResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
      thinking_content?: string;
      tool_calls?: any[];
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface ModelValidationResult {
  isValid: boolean;
  hasAccess: boolean;
  errors: string[];
  warnings: string[];
  suggestedModel?: string;
  upgradeRequired?: boolean;
}

// Configuration Constants
export const GLM_MODEL_IDS = {
  GLM_4_5: 'glm-4.5',
  GLM_4_5_AIR: 'glm-4.5-air',
  GLM_4_5_X: 'glm-4.5-x',
  GLM_4_5_AIRX: 'glm-4.5-airx',
  GLM_4_5_FLASH: 'glm-4.5-flash'
} as const;

export type GLMModelId = typeof GLM_MODEL_IDS[keyof typeof GLM_MODEL_IDS];

export const SUBSCRIPTION_TIERS = {
  FREE: 'free' as const,
  BASIC: 'basic' as const,
  PREMIUM: 'premium' as const
};

export const MODEL_SPEEDS = {
  STANDARD: 'standard' as const,
  FAST: 'fast' as const,
  ULTRA_FAST: 'ultra-fast' as const
};

export const REQUEST_TYPES = {
  CHAT: 'chat' as const,
  THINKING: 'thinking' as const,
  STRUCTURED: 'structured' as const,
  FUNCTION_CALL: 'function_call' as const
};

// Error Types
export class ModelConfigurationError extends Error {
  constructor(
    message: string,
    public modelId?: string,
    public tier?: SubscriptionTier
  ) {
    super(message);
    this.name = 'ModelConfigurationError';
  }
}

export class SubscriptionValidationError extends Error {
  constructor(
    message: string,
    public requiredTier?: SubscriptionTier,
    public currentTier?: SubscriptionTier
  ) {
    super(message);
    this.name = 'SubscriptionValidationError';
  }
}

export class ModelAccessError extends Error {
  constructor(
    message: string,
    public modelId?: string,
    public tier?: SubscriptionTier,
    public upgradeUrl?: string
  ) {
    super(message);
    this.name = 'ModelAccessError';
  }
}
