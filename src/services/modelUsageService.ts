/**
 * Model Usage Service
 * Tracks and analyzes model usage, costs, and performance metrics
 */

import { PrismaClient } from '@prisma/client';
import {
  ModelUsageMetrics,
  RequestType,
  GLMModelId,
  REQUEST_TYPES
} from '../types/glm-models';
import { modelRegistry } from '../config/glm-model-registry';

const prisma = new PrismaClient();

export interface CreateUsageData {
  userId?: string;
  conversationId?: string;
  modelId: string;
  requestType: RequestType;
  inputTokens: number;
  outputTokens: number;
  responseTime?: number;
  success: boolean;
  errorCode?: string;
  errorMessage?: string;
  metadata?: any;
}

export interface UsageAnalytics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  totalTokens: number;
  totalCost: number;
  avgResponseTime: number;
  uniqueUsers: number;
  modelBreakdown: Record<string, {
    requests: number;
    tokens: number;
    cost: number;
  }>;
}

export interface UserUsageStats {
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  requestsByModel: Record<string, number>;
  requestsByType: Record<RequestType, number>;
  avgResponseTime: number;
  successRate: number;
}

export class ModelUsageService {
  /**
   * Record a model usage event
   */
  async recordUsage(data: CreateUsageData): Promise<void> {
    const totalTokens = data.inputTokens + data.outputTokens;
    const cost = modelRegistry.calculateCost(data.modelId, data.inputTokens, data.outputTokens);

    await prisma.modelUsage.create({
      data: {
        userId: data.userId,
        conversationId: data.conversationId,
        modelId: data.modelId,
        requestType: data.requestType,
        inputTokens: data.inputTokens,
        outputTokens: data.outputTokens,
        totalTokens,
        cost,
        responseTime: data.responseTime,
        success: data.success,
        errorCode: data.errorCode,
        errorMessage: data.errorMessage,
        metadata: data.metadata
      }
    });

    // Update analytics asynchronously
    this.updateAnalytics(data.modelId).catch(console.error);
  }

  /**
   * Get user usage statistics
   */
  async getUserUsageStats(
    userId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<UserUsageStats> {
    const whereClause: any = { userId };
    
    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt.gte = startDate;
      if (endDate) whereClause.createdAt.lte = endDate;
    }

    const usage = await prisma.modelUsage.findMany({
      where: whereClause,
      select: {
        modelId: true,
        requestType: true,
        totalTokens: true,
        cost: true,
        responseTime: true,
        success: true
      }
    });

    const totalRequests = usage.length;
    const totalTokens = usage.reduce((sum, u) => sum + u.totalTokens, 0);
    const totalCost = usage.reduce((sum, u) => sum + u.cost, 0);
    const successfulRequests = usage.filter(u => u.success).length;
    const avgResponseTime = usage
      .filter(u => u.responseTime)
      .reduce((sum, u, _, arr) => sum + (u.responseTime! / arr.length), 0);

    const requestsByModel: Record<string, number> = {};
    const requestsByType: Record<RequestType, number> = {
      [REQUEST_TYPES.CHAT]: 0,
      [REQUEST_TYPES.THINKING]: 0,
      [REQUEST_TYPES.STRUCTURED]: 0,
      [REQUEST_TYPES.FUNCTION_CALL]: 0
    };

    usage.forEach(u => {
      requestsByModel[u.modelId] = (requestsByModel[u.modelId] || 0) + 1;
      requestsByType[u.requestType as RequestType]++;
    });

    return {
      totalRequests,
      totalTokens,
      totalCost,
      requestsByModel,
      requestsByType,
      avgResponseTime,
      successRate: totalRequests > 0 ? successfulRequests / totalRequests : 0
    };
  }

  /**
   * Get conversation usage statistics
   */
  async getConversationUsage(conversationId: string): Promise<{
    totalTokens: number;
    totalCost: number;
    requestCount: number;
    modelBreakdown: Record<string, { tokens: number; cost: number; requests: number }>;
  }> {
    const usage = await prisma.modelUsage.findMany({
      where: { conversationId },
      select: {
        modelId: true,
        totalTokens: true,
        cost: true
      }
    });

    const totalTokens = usage.reduce((sum, u) => sum + u.totalTokens, 0);
    const totalCost = usage.reduce((sum, u) => sum + u.cost, 0);
    const requestCount = usage.length;

    const modelBreakdown: Record<string, { tokens: number; cost: number; requests: number }> = {};
    
    usage.forEach(u => {
      if (!modelBreakdown[u.modelId]) {
        modelBreakdown[u.modelId] = { tokens: 0, cost: 0, requests: 0 };
      }
      modelBreakdown[u.modelId].tokens += u.totalTokens;
      modelBreakdown[u.modelId].cost += u.cost;
      modelBreakdown[u.modelId].requests++;
    });

    return {
      totalTokens,
      totalCost,
      requestCount,
      modelBreakdown
    };
  }

  /**
   * Get global usage analytics
   */
  async getGlobalAnalytics(
    startDate?: Date,
    endDate?: Date
  ): Promise<UsageAnalytics> {
    const whereClause: any = {};
    
    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt.gte = startDate;
      if (endDate) whereClause.createdAt.lte = endDate;
    }

    const [usage, uniqueUsersResult] = await Promise.all([
      prisma.modelUsage.findMany({
        where: whereClause,
        select: {
          modelId: true,
          totalTokens: true,
          cost: true,
          responseTime: true,
          success: true
        }
      }),
      prisma.modelUsage.findMany({
        where: {
          ...whereClause,
          userId: { not: null }
        },
        select: { userId: true },
        distinct: ['userId']
      })
    ]);

    const totalRequests = usage.length;
    const successfulRequests = usage.filter(u => u.success).length;
    const failedRequests = totalRequests - successfulRequests;
    const totalTokens = usage.reduce((sum, u) => sum + u.totalTokens, 0);
    const totalCost = usage.reduce((sum, u) => sum + u.cost, 0);
    const avgResponseTime = usage
      .filter(u => u.responseTime)
      .reduce((sum, u, _, arr) => sum + (u.responseTime! / arr.length), 0);
    const uniqueUsers = uniqueUsersResult.length;

    const modelBreakdown: Record<string, { requests: number; tokens: number; cost: number }> = {};
    
    usage.forEach(u => {
      if (!modelBreakdown[u.modelId]) {
        modelBreakdown[u.modelId] = { requests: 0, tokens: 0, cost: 0 };
      }
      modelBreakdown[u.modelId].requests++;
      modelBreakdown[u.modelId].tokens += u.totalTokens;
      modelBreakdown[u.modelId].cost += u.cost;
    });

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      totalTokens,
      totalCost,
      avgResponseTime,
      uniqueUsers,
      modelBreakdown
    };
  }

  /**
   * Get model performance metrics
   */
  async getModelPerformance(modelId: string, days: number = 7): Promise<{
    avgResponseTime: number;
    successRate: number;
    requestCount: number;
    errorBreakdown: Record<string, number>;
  }> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const usage = await prisma.modelUsage.findMany({
      where: {
        modelId,
        createdAt: { gte: startDate }
      },
      select: {
        responseTime: true,
        success: true,
        errorCode: true
      }
    });

    const requestCount = usage.length;
    const successfulRequests = usage.filter(u => u.success).length;
    const successRate = requestCount > 0 ? successfulRequests / requestCount : 0;
    const avgResponseTime = usage
      .filter(u => u.responseTime)
      .reduce((sum, u, _, arr) => sum + (u.responseTime! / arr.length), 0);

    const errorBreakdown: Record<string, number> = {};
    usage
      .filter(u => !u.success && u.errorCode)
      .forEach(u => {
        errorBreakdown[u.errorCode!] = (errorBreakdown[u.errorCode!] || 0) + 1;
      });

    return {
      avgResponseTime,
      successRate,
      requestCount,
      errorBreakdown
    };
  }

  /**
   * Update analytics for a model (called asynchronously)
   */
  private async updateAnalytics(modelId: string): Promise<void> {
    const now = new Date();
    const hourStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours());
    const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Update hourly analytics
    await this.upsertAnalytics(modelId, 'hourly', hourStart);
    
    // Update daily analytics
    await this.upsertAnalytics(modelId, 'daily', dayStart);
  }

  /**
   * Upsert analytics record
   */
  private async upsertAnalytics(
    modelId: string,
    period: string,
    periodStart: Date
  ): Promise<void> {
    const periodEnd = new Date(periodStart);
    if (period === 'hourly') {
      periodEnd.setHours(periodEnd.getHours() + 1);
    } else {
      periodEnd.setDate(periodEnd.getDate() + 1);
    }

    const usage = await prisma.modelUsage.findMany({
      where: {
        modelId,
        createdAt: {
          gte: periodStart,
          lt: periodEnd
        }
      },
      select: {
        totalTokens: true,
        cost: true,
        responseTime: true,
        success: true,
        userId: true
      }
    });

    const totalRequests = usage.length;
    const successfulRequests = usage.filter(u => u.success).length;
    const failedRequests = totalRequests - successfulRequests;
    const totalTokens = usage.reduce((sum, u) => sum + u.totalTokens, 0);
    const totalCost = usage.reduce((sum, u) => sum + u.cost, 0);
    const avgResponseTime = usage
      .filter(u => u.responseTime)
      .reduce((sum, u, _, arr) => sum + (u.responseTime! / arr.length), 0) || 0;
    const uniqueUsers = new Set(usage.filter(u => u.userId).map(u => u.userId)).size;

    await prisma.modelUsageAnalytics.upsert({
      where: {
        modelId_period_periodStart: {
          modelId,
          period,
          periodStart
        }
      },
      update: {
        totalRequests,
        successfulRequests,
        failedRequests,
        totalTokens,
        totalCost,
        avgResponseTime,
        uniqueUsers,
        updatedAt: new Date()
      },
      create: {
        modelId,
        period,
        periodStart,
        periodEnd,
        totalRequests,
        successfulRequests,
        failedRequests,
        totalTokens,
        totalCost,
        avgResponseTime,
        uniqueUsers
      }
    });
  }
}

export const modelUsageService = new ModelUsageService();
export default modelUsageService;
