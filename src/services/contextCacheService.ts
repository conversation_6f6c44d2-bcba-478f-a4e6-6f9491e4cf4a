/**
 * Context Cache Service
 * Manages conversation context caching with TTL and compression
 */

import { PrismaClient } from '@prisma/client';
import { ContextCacheEntry } from '../types/glm-models';
import { createHash } from 'crypto';
import { gzip, gunzip } from 'zlib';
import { promisify } from 'util';

const prisma = new PrismaClient();
const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

export interface CacheOptions {
  ttlHours?: number;
  enableCompression?: boolean;
  maxCacheSize?: number; // in bytes
}

export interface ConversationContext {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
  }>;
  metadata?: {
    modelId?: string;
    temperature?: number;
    maxTokens?: number;
    [key: string]: any;
  };
}

export class ContextCacheService {
  private readonly defaultTTLHours = 24;
  private readonly defaultMaxCacheSize = 1024 * 1024; // 1MB

  /**
   * Generate cache key for conversation context
   */
  private generateCacheKey(conversationId: string, contextHash?: string): string {
    const baseKey = `conv:${conversationId}`;
    if (contextHash) {
      return `${baseKey}:${contextHash}`;
    }
    return baseKey;
  }

  /**
   * Generate hash for conversation context
   */
  private generateContextHash(context: ConversationContext): string {
    const contextString = JSON.stringify({
      messages: context.messages.map(m => ({ role: m.role, content: m.content })),
      metadata: context.metadata
    });
    return createHash('sha256').update(contextString).digest('hex').substring(0, 16);
  }

  /**
   * Compress context data
   */
  private async compressContext(context: ConversationContext): Promise<{
    compressed: string;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
  }> {
    const contextString = JSON.stringify(context);
    const originalSize = Buffer.byteLength(contextString, 'utf8');
    
    const compressed = await gzipAsync(contextString);
    const compressedSize = compressed.length;
    const compressionRatio = originalSize / compressedSize;
    
    return {
      compressed: compressed.toString('base64'),
      originalSize,
      compressedSize,
      compressionRatio
    };
  }

  /**
   * Decompress context data
   */
  private async decompressContext(compressedData: string): Promise<ConversationContext> {
    const compressed = Buffer.from(compressedData, 'base64');
    const decompressed = await gunzipAsync(compressed);
    return JSON.parse(decompressed.toString('utf8'));
  }

  /**
   * Calculate token count for context
   */
  private calculateTokenCount(context: ConversationContext): number {
    // Rough estimation: 1 token ≈ 4 characters
    const totalChars = context.messages.reduce((sum, msg) => sum + msg.content.length, 0);
    return Math.ceil(totalChars / 4);
  }

  /**
   * Store conversation context in cache
   */
  async storeContext(
    conversationId: string,
    context: ConversationContext,
    options: CacheOptions = {}
  ): Promise<ContextCacheEntry> {
    const {
      ttlHours = this.defaultTTLHours,
      enableCompression = true,
      maxCacheSize = this.defaultMaxCacheSize
    } = options;

    const contextHash = this.generateContextHash(context);
    const cacheKey = this.generateCacheKey(conversationId, contextHash);
    
    // Check if context already exists
    const existing = await this.getContext(conversationId, contextHash);
    if (existing) {
      // Update hit count and last accessed
      await prisma.contextCache.update({
        where: { id: existing.id },
        data: {
          hitCount: { increment: 1 },
          lastAccessed: new Date()
        }
      });
      return existing;
    }

    let contextData: string;
    let compressionRatio: number | undefined;
    let tokenCount: number;

    if (enableCompression) {
      const compressed = await this.compressContext(context);
      
      // Check if compressed size exceeds limit
      if (compressed.compressedSize > maxCacheSize) {
        throw new Error(`Compressed context size (${compressed.compressedSize}) exceeds limit (${maxCacheSize})`);
      }
      
      contextData = compressed.compressed;
      compressionRatio = compressed.compressionRatio;
      tokenCount = this.calculateTokenCount(context);
    } else {
      contextData = JSON.stringify(context);
      const dataSize = Buffer.byteLength(contextData, 'utf8');
      
      if (dataSize > maxCacheSize) {
        throw new Error(`Context size (${dataSize}) exceeds limit (${maxCacheSize})`);
      }
      
      tokenCount = this.calculateTokenCount(context);
    }

    const ttl = new Date();
    ttl.setHours(ttl.getHours() + ttlHours);

    const cacheEntry = await prisma.contextCache.create({
      data: {
        conversationId,
        cacheKey,
        contextData,
        tokenCount,
        compressionRatio,
        ttl,
        hitCount: 1,
        lastAccessed: new Date()
      }
    });

    return {
      id: cacheEntry.id,
      conversationId: cacheEntry.conversationId,
      cacheKey: cacheEntry.cacheKey,
      contextData: cacheEntry.contextData,
      tokenCount: cacheEntry.tokenCount,
      compressionRatio: cacheEntry.compressionRatio || undefined,
      ttl: cacheEntry.ttl,
      hitCount: cacheEntry.hitCount,
      lastAccessed: cacheEntry.lastAccessed,
      createdAt: cacheEntry.createdAt,
      updatedAt: cacheEntry.updatedAt
    };
  }

  /**
   * Retrieve conversation context from cache
   */
  async getContext(
    conversationId: string,
    contextHash?: string
  ): Promise<ContextCacheEntry | null> {
    const cacheKey = this.generateCacheKey(conversationId, contextHash);
    
    const cacheEntry = await prisma.contextCache.findUnique({
      where: { cacheKey }
    });

    if (!cacheEntry) {
      return null;
    }

    // Check if cache has expired
    if (cacheEntry.ttl < new Date()) {
      await this.deleteContext(cacheEntry.id);
      return null;
    }

    // Update hit count and last accessed
    await prisma.contextCache.update({
      where: { id: cacheEntry.id },
      data: {
        hitCount: { increment: 1 },
        lastAccessed: new Date()
      }
    });

    return {
      id: cacheEntry.id,
      conversationId: cacheEntry.conversationId,
      cacheKey: cacheEntry.cacheKey,
      contextData: cacheEntry.contextData,
      tokenCount: cacheEntry.tokenCount,
      compressionRatio: cacheEntry.compressionRatio || undefined,
      ttl: cacheEntry.ttl,
      hitCount: cacheEntry.hitCount + 1,
      lastAccessed: new Date(),
      createdAt: cacheEntry.createdAt,
      updatedAt: cacheEntry.updatedAt
    };
  }

  /**
   * Retrieve and decompress conversation context
   */
  async getDecompressedContext(
    conversationId: string,
    contextHash?: string
  ): Promise<ConversationContext | null> {
    const cacheEntry = await this.getContext(conversationId, contextHash);
    
    if (!cacheEntry) {
      return null;
    }

    try {
      if (cacheEntry.compressionRatio) {
        // Data is compressed
        return await this.decompressContext(cacheEntry.contextData);
      } else {
        // Data is not compressed
        return JSON.parse(cacheEntry.contextData);
      }
    } catch (error) {
      console.error('Failed to decompress context:', error);
      // Delete corrupted cache entry
      await this.deleteContext(cacheEntry.id);
      return null;
    }
  }

  /**
   * Delete context from cache
   */
  async deleteContext(cacheId: string): Promise<void> {
    await prisma.contextCache.delete({
      where: { id: cacheId }
    });
  }

  /**
   * Delete all contexts for a conversation
   */
  async deleteConversationContexts(conversationId: string): Promise<void> {
    await prisma.contextCache.deleteMany({
      where: { conversationId }
    });
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredEntries(): Promise<number> {
    const result = await prisma.contextCache.deleteMany({
      where: {
        ttl: { lt: new Date() }
      }
    });

    return result.count;
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    totalEntries: number;
    totalSize: number;
    avgCompressionRatio: number;
    hitRateByConversation: Record<string, number>;
    expiredEntries: number;
  }> {
    const [entries, expiredCount] = await Promise.all([
      prisma.contextCache.findMany({
        select: {
          conversationId: true,
          tokenCount: true,
          compressionRatio: true,
          hitCount: true,
          ttl: true
        }
      }),
      prisma.contextCache.count({
        where: { ttl: { lt: new Date() } }
      })
    ]);

    const totalEntries = entries.length;
    const totalSize = entries.reduce((sum, entry) => sum + entry.tokenCount, 0);
    const avgCompressionRatio = entries
      .filter(e => e.compressionRatio)
      .reduce((sum, e, _, arr) => sum + (e.compressionRatio! / arr.length), 0) || 1;

    const hitRateByConversation: Record<string, number> = {};
    entries.forEach(entry => {
      if (!hitRateByConversation[entry.conversationId]) {
        hitRateByConversation[entry.conversationId] = 0;
      }
      hitRateByConversation[entry.conversationId] += entry.hitCount;
    });

    return {
      totalEntries,
      totalSize,
      avgCompressionRatio,
      hitRateByConversation,
      expiredEntries: expiredCount
    };
  }

  /**
   * Update cache TTL
   */
  async updateCacheTTL(cacheId: string, ttlHours: number): Promise<void> {
    const newTTL = new Date();
    newTTL.setHours(newTTL.getHours() + ttlHours);

    await prisma.contextCache.update({
      where: { id: cacheId },
      data: { ttl: newTTL }
    });
  }

  /**
   * Get conversation cache entries
   */
  async getConversationCacheEntries(conversationId: string): Promise<ContextCacheEntry[]> {
    const entries = await prisma.contextCache.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'desc' }
    });

    return entries.map(entry => ({
      id: entry.id,
      conversationId: entry.conversationId,
      cacheKey: entry.cacheKey,
      contextData: entry.contextData,
      tokenCount: entry.tokenCount,
      compressionRatio: entry.compressionRatio || undefined,
      ttl: entry.ttl,
      hitCount: entry.hitCount,
      lastAccessed: entry.lastAccessed,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt
    }));
  }
}

export const contextCacheService = new ContextCacheService();
export default contextCacheService;
