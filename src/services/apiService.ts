import { apiConfig } from '@/config/api';
import { securityManager } from '@/config/security';
import { vercelDb } from '@/lib/database-integration';
import { modelRegistry } from '@/config/glm-model-registry';
import { subscriptionService } from '@/services/subscriptionService';
import { modelUsageService } from '@/services/modelUsageService';
import { contextCacheService } from '@/services/contextCacheService';
import {
  GLMRequest,
  GLMResponse,
  GLMModelId,
  RequestType,
  ModelValidationResult,
  REQUEST_TYPES,
  ModelAccessError,
  SubscriptionValidationError
} from '@/types/glm-models';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// Enhanced GLM-4.5 Multi-Model Interfaces
export interface EnhancedChatRequest {
  messages: ChatMessage[];
  modelId?: GLMModelId;
  userId?: string;
  conversationId?: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  stream?: boolean;
  thinking?: {
    enabled: boolean;
    depth?: 'basic' | 'advanced' | 'deep';
    budget_tokens?: number;
  };
  structured_output?: {
    enabled: boolean;
    schema?: any;
    format?: 'json' | 'yaml';
  };
  function_calling?: {
    enabled: boolean;
    tools?: any[];
    tool_choice?: any;
  };
  context_caching?: {
    enabled: boolean;
    ttl_hours?: number;
    use_compression?: boolean;
  };
  metadata?: {
    request_type?: RequestType;
    priority?: 'low' | 'normal' | 'high';
    [key: string]: any;
  };
}

export interface EnhancedGLMResponse {
  success: boolean;
  data?: {
    message: string;
    id: string;
    timestamp: Date;
    thinking_content?: string;
    tool_calls?: any[];
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
    responseTime?: number;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface ApiResponse {
  success: boolean;
  data?: {
    message: string;
    id: string;
    timestamp: Date;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface StreamCallback {
  onToken: (token: string) => void;
  onComplete: (message: string) => void;
  onError: (error: Error) => void;
}

// Video Generation Interfaces
export interface VideoGenerationRequest {
  prompt: string;
  model?: string;
  width?: number;
  height?: number;
  duration?: number;
  fps?: number;
  steps?: number;
  cfg_scale?: number;
  seed?: number;
}

export interface VideoGenerationResponse {
  success: boolean;
  data?: {
    video_id: string;
    status: 'processing' | 'completed' | 'failed';
    video_url?: string;
    thumbnail_url?: string;
    progress: number;
    estimated_time?: number;
    created_at: Date;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface VideoStatusResponse {
  success: boolean;
  data?: {
    video_id: string;
    status: 'processing' | 'completed' | 'failed';
    video_url?: string;
    thumbnail_url?: string;
    progress: number;
    estimated_time?: number;
    error_message?: string;
    created_at: Date;
    completed_at?: Date;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// Enhanced GLM-4.5 Function Calling Interfaces
export interface FunctionCall {
  name: string;
  parameters: Record<string, any>;
}

export interface FunctionResponse {
  name: string;
  response: any;
}

export interface EnhancedGLMRequest {
  messages: ChatMessage[];
  functions?: FunctionCall[];
  function_call?: 'auto' | 'none' | { name: string };
  tools?: any[];
  tool_choice?: 'auto' | 'none' | { type: string; function?: { name: string } };
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  thinking?: {
    type: 'enabled' | 'disabled';
    budget_tokens?: number;
  };
}

export interface EnhancedGLMResponse {
  success: boolean;
  data?: {
    message: string;
    id: string;
    timestamp: Date;
    function_calls?: FunctionCall[];
    tool_calls?: any[];
    thinking_content?: string;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// Circuit breaker state for API resilience
interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
}

class ApiService {
  private static instance: ApiService;
  private circuitBreaker: CircuitBreakerState = {
    failures: 0,
    lastFailureTime: 0,
    state: 'CLOSED'
  };
  private readonly maxFailures = 5;
  private readonly resetTimeout = 60000; // 1 minute
  private readonly halfOpenMaxCalls = 3;
  private halfOpenCalls = 0;
  
  private constructor() {}
  
  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  /**
   * Enhanced GLM-4.5 Multi-Model Chat API
   * Supports all GLM-4.5 variants with subscription validation, context caching, and usage tracking
   */
  async enhancedGLMChat(request: EnhancedChatRequest): Promise<EnhancedGLMResponse> {
    const startTime = Date.now();
    let selectedModel: GLMModelId;
    let requestType: RequestType = REQUEST_TYPES.CHAT;

    try {
      // 1. Validate and select model
      const modelValidation = await this.validateAndSelectModel(request);
      if (!modelValidation.isValid) {
        return {
          success: false,
          error: {
            code: 'MODEL_VALIDATION_FAILED',
            message: modelValidation.errors.join(', '),
            details: modelValidation
          }
        };
      }
      selectedModel = modelValidation.selectedModel!;

      // 2. Determine request type
      requestType = this.determineRequestType(request);

      // 3. Check context cache if enabled
      let cachedContext = null;
      if (request.context_caching?.enabled && request.conversationId) {
        cachedContext = await this.checkContextCache(request.conversationId, request.messages);
      }

      // 4. Prepare GLM request
      const glmRequest = await this.prepareGLMRequest(request, selectedModel, cachedContext);

      // 5. Execute API call with circuit breaker
      const circuitCheck = this.checkCircuitBreaker();
      if (!circuitCheck.allowed) {
        return {
          success: false,
          error: {
            code: 'CIRCUIT_BREAKER_OPEN',
            message: circuitCheck.reason || 'Service temporarily unavailable'
          }
        };
      }

      const response = await this.executeGLMRequest(glmRequest, selectedModel);
      const responseTime = Date.now() - startTime;

      // 6. Process response
      const processedResponse = await this.processGLMResponse(response, request, selectedModel, responseTime);

      // 7. Record usage metrics
      if (request.userId) {
        await this.recordUsageMetrics({
          userId: request.userId,
          conversationId: request.conversationId,
          modelId: selectedModel,
          requestType,
          inputTokens: processedResponse.data?.usage?.prompt_tokens || 0,
          outputTokens: processedResponse.data?.usage?.completion_tokens || 0,
          responseTime,
          success: processedResponse.success
        });
      }

      // 8. Update context cache if enabled
      if (request.context_caching?.enabled && request.conversationId && processedResponse.success) {
        await this.updateContextCache(request, processedResponse, selectedModel);
      }

      // 9. Update circuit breaker on success
      this.onApiSuccess();

      return processedResponse;

    } catch (error) {
      const responseTime = Date.now() - startTime;

      // Record failed usage
      if (request.userId && selectedModel!) {
        await this.recordUsageMetrics({
          userId: request.userId,
          conversationId: request.conversationId,
          modelId: selectedModel!,
          requestType,
          inputTokens: 0,
          outputTokens: 0,
          responseTime,
          success: false,
          errorCode: error instanceof Error ? error.name : 'UNKNOWN_ERROR',
          errorMessage: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      }

      // Update circuit breaker on failure
      this.onApiFailure();

      return {
        success: false,
        error: {
          code: error instanceof Error ? error.name : 'UNKNOWN_ERROR',
          message: error instanceof Error ? error.message : 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Validate model access and select appropriate model
   */
  private async validateAndSelectModel(request: EnhancedChatRequest): Promise<ModelValidationResult & { selectedModel?: GLMModelId }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let selectedModel: GLMModelId | undefined;
    let hasAccess = false;
    let upgradeRequired = false;

    try {
      // Get user's subscription tier
      const userTier = request.userId
        ? await subscriptionService.getUserTier(request.userId)
        : 'free';

      // If model is specified, validate access
      if (request.modelId) {
        hasAccess = await subscriptionService.validateModelAccess(request.userId || '', request.modelId);
        if (hasAccess) {
          selectedModel = request.modelId;
        } else {
          errors.push(`Access denied to model ${request.modelId}. Upgrade required.`);
          upgradeRequired = true;

          // Suggest alternative model
          const defaultModel = await subscriptionService.getDefaultModel(request.userId || '');
          if (defaultModel) {
            selectedModel = defaultModel.id as GLMModelId;
            warnings.push(`Using ${defaultModel.displayName} instead of ${request.modelId}`);
            hasAccess = true;
          }
        }
      } else {
        // Auto-select best available model
        const defaultModel = await subscriptionService.getDefaultModel(request.userId || '');
        if (defaultModel) {
          selectedModel = defaultModel.id as GLMModelId;
          hasAccess = true;
        } else {
          errors.push('No models available for your subscription tier');
        }
      }

      // Validate model capabilities against request features
      if (selectedModel) {
        const modelConfig = modelRegistry.getModel(selectedModel);
        if (modelConfig) {
          // Check thinking mode support
          if (request.thinking?.enabled && !modelConfig.capabilities.supportsThinking) {
            warnings.push(`Model ${selectedModel} does not support thinking mode. Feature disabled.`);
          }

          // Check structured output support
          if (request.structured_output?.enabled && !modelConfig.capabilities.supportsStructuredOutput) {
            warnings.push(`Model ${selectedModel} does not support structured output. Feature disabled.`);
          }

          // Check function calling support
          if (request.function_calling?.enabled && !modelConfig.capabilities.supportsFunctionCalling) {
            warnings.push(`Model ${selectedModel} does not support function calling. Feature disabled.`);
          }

          // Check token limits
          const requestedTokens = request.max_tokens || modelConfig.capabilities.maxTokens;
          if (requestedTokens > modelConfig.capabilities.maxTokens) {
            warnings.push(`Requested tokens (${requestedTokens}) exceed model limit (${modelConfig.capabilities.maxTokens}). Using maximum allowed.`);
          }
        }
      }

      return {
        isValid: errors.length === 0 && hasAccess && selectedModel !== undefined,
        hasAccess,
        errors,
        warnings,
        selectedModel,
        upgradeRequired
      };

    } catch (error) {
      errors.push(`Model validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        isValid: false,
        hasAccess: false,
        errors,
        warnings
      };
    }
  }

  /**
   * Determine request type based on features
   */
  private determineRequestType(request: EnhancedChatRequest): RequestType {
    if (request.thinking?.enabled) {
      return REQUEST_TYPES.THINKING;
    }
    if (request.structured_output?.enabled) {
      return REQUEST_TYPES.STRUCTURED;
    }
    if (request.function_calling?.enabled) {
      return REQUEST_TYPES.FUNCTION_CALL;
    }
    return REQUEST_TYPES.CHAT;
  }

  /**
   * Check context cache for conversation
   */
  private async checkContextCache(conversationId: string, messages: ChatMessage[]) {
    try {
      const contextHash = this.generateContextHash(messages);
      return await contextCacheService.getDecompressedContext(conversationId, contextHash);
    } catch (error) {
      console.warn('Context cache check failed:', error);
      return null;
    }
  }

  /**
   * Generate context hash for caching
   */
  private generateContextHash(messages: ChatMessage[]): string {
    const contextString = JSON.stringify(messages.map(m => ({ role: m.role, content: m.content })));
    return require('crypto').createHash('sha256').update(contextString).digest('hex').substring(0, 16);
  }

  /**
   * Circuit breaker pattern implementation for API resilience
   */
  private checkCircuitBreaker(): { allowed: boolean; reason?: string } {
    const now = Date.now();
    
    switch (this.circuitBreaker.state) {
      case 'CLOSED':
        // Normal operation
        return { allowed: true };
        
      case 'OPEN':
        // Check if we should transition to half-open
        if (now - this.circuitBreaker.lastFailureTime > this.resetTimeout) {
          this.circuitBreaker.state = 'HALF_OPEN';
          this.halfOpenCalls = 0;
          this.logDetailedError('CIRCUIT_BREAKER_HALF_OPEN', {
            failures: this.circuitBreaker.failures,
            timeSinceLastFailure: now - this.circuitBreaker.lastFailureTime
          });
          return { allowed: true };
        }
        return { 
          allowed: false, 
          reason: `Circuit breaker is OPEN. Too many failures (${this.circuitBreaker.failures}). Will retry after ${new Date(this.circuitBreaker.lastFailureTime + this.resetTimeout).toLocaleTimeString()}`
        };
        
      case 'HALF_OPEN':
        // Allow limited calls to test if service is recovered
        if (this.halfOpenCalls < this.halfOpenMaxCalls) {
          this.halfOpenCalls++;
          return { allowed: true };
        }
        return { 
          allowed: false, 
          reason: 'Circuit breaker is HALF_OPEN and max test calls reached'
        };
        
      default:
        return { allowed: true };
    }
  }

  /**
   * Record API call success for circuit breaker
   */
  private recordSuccess(): void {
    if (this.circuitBreaker.state === 'HALF_OPEN') {
      // Successful call in half-open state - reset circuit breaker
      this.circuitBreaker.state = 'CLOSED';
      this.circuitBreaker.failures = 0;
      this.halfOpenCalls = 0;
      this.logDetailedError('CIRCUIT_BREAKER_RESET', {
        previousFailures: this.circuitBreaker.failures
      });
    } else if (this.circuitBreaker.failures > 0) {
      // Gradual recovery - reduce failure count
      this.circuitBreaker.failures = Math.max(0, this.circuitBreaker.failures - 1);
    }
  }

  /**
   * Record API call failure for circuit breaker
   */
  private recordFailure(): void {
    this.circuitBreaker.failures++;
    this.circuitBreaker.lastFailureTime = Date.now();
    
    if (this.circuitBreaker.failures >= this.maxFailures) {
      this.circuitBreaker.state = 'OPEN';
      this.logDetailedError('CIRCUIT_BREAKER_OPENED', {
        failures: this.circuitBreaker.failures,
        maxFailures: this.maxFailures,
        resetTime: new Date(this.circuitBreaker.lastFailureTime + this.resetTimeout).toISOString()
      });
    }
  }

  /**
   * Get circuit breaker status
   */
  public getCircuitBreakerStatus(): {
    state: string;
    failures: number;
    healthy: boolean;
    nextRetryTime?: Date;
  } {
    return {
      state: this.circuitBreaker.state,
      failures: this.circuitBreaker.failures,
      healthy: this.circuitBreaker.state === 'CLOSED' && this.circuitBreaker.failures === 0,
      nextRetryTime: this.circuitBreaker.state === 'OPEN' 
        ? new Date(this.circuitBreaker.lastFailureTime + this.resetTimeout)
        : undefined
    };
  }

  /**
   * Prepare GLM request with model-specific optimizations
   */
  private async prepareGLMRequest(
    request: EnhancedChatRequest,
    modelId: GLMModelId,
    cachedContext: any
  ): Promise<GLMRequest> {
    const modelConfig = modelRegistry.getModel(modelId)!;

    // Use cached context if available
    const messages = cachedContext?.messages || request.messages;

    const glmRequest: GLMRequest = {
      model: modelId,
      messages: messages.map(m => ({
        role: m.role,
        content: m.content
      })),
      temperature: request.temperature,
      max_tokens: Math.min(
        request.max_tokens || modelConfig.capabilities.maxTokens,
        modelConfig.capabilities.maxTokens
      ),
      top_p: request.top_p,
      stream: request.stream
    };

    // Add thinking mode if supported
    if (request.thinking?.enabled && modelConfig.capabilities.supportsThinking) {
      glmRequest.thinking = true;
    }

    // Add structured output if supported
    if (request.structured_output?.enabled && modelConfig.capabilities.supportsStructuredOutput) {
      glmRequest.response_format = {
        type: 'json_object',
        schema: request.structured_output.schema
      };
    }

    // Add function calling if supported
    if (request.function_calling?.enabled && modelConfig.capabilities.supportsFunctionCalling) {
      glmRequest.tools = request.function_calling.tools;
      glmRequest.tool_choice = request.function_calling.tool_choice;
    }

    return glmRequest;
  }

  /**
   * Execute GLM request with retry logic
   */
  private async executeGLMRequest(request: GLMRequest, modelId: GLMModelId): Promise<GLMResponse> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(`${apiConfig.endpoint}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiConfig.apiKey}`,
            'X-Model-ID': modelId,
            'X-Request-ID': this.generateRequestId()
          },
          body: JSON.stringify(request)
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error('All retry attempts failed');
  }

  /**
   * Process GLM response and extract relevant data
   */
  private async processGLMResponse(
    response: GLMResponse,
    originalRequest: EnhancedChatRequest,
    modelId: GLMModelId,
    responseTime: number
  ): Promise<EnhancedGLMResponse> {
    try {
      if (!response.choices || response.choices.length === 0) {
        throw new Error('No response choices received');
      }

      const choice = response.choices[0];
      const message = choice.message;

      return {
        success: true,
        data: {
          message: message.content,
          id: response.id,
          timestamp: new Date(),
          thinking_content: message.thinking_content,
          tool_calls: message.tool_calls,
          usage: response.usage
        }
      };

    } catch (error) {
      return {
        success: false,
        error: {
          code: 'RESPONSE_PROCESSING_ERROR',
          message: error instanceof Error ? error.message : 'Failed to process response',
          details: { response, originalRequest, modelId, responseTime }
        }
      };
    }
  }

  /**
   * Record usage metrics
   */
  private async recordUsageMetrics(data: {
    userId?: string;
    conversationId?: string;
    modelId: GLMModelId;
    requestType: RequestType;
    inputTokens: number;
    outputTokens: number;
    responseTime: number;
    success: boolean;
    errorCode?: string;
    errorMessage?: string;
  }): Promise<void> {
    try {
      await modelUsageService.recordUsage({
        userId: data.userId,
        conversationId: data.conversationId,
        modelId: data.modelId,
        requestType: data.requestType,
        inputTokens: data.inputTokens,
        outputTokens: data.outputTokens,
        responseTime: data.responseTime,
        success: data.success,
        errorCode: data.errorCode,
        errorMessage: data.errorMessage
      });
    } catch (error) {
      console.error('Failed to record usage metrics:', error);
    }
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle API success for circuit breaker
   */
  private onApiSuccess(): void {
    if (this.circuitBreaker.state === 'HALF_OPEN') {
      // Reset circuit breaker on successful half-open call
      this.circuitBreaker.state = 'CLOSED';
      this.circuitBreaker.failures = 0;
      this.halfOpenCalls = 0;
    }
  }

  /**
   * Handle API failure for circuit breaker
   */
  private onApiFailure(): void {
    this.circuitBreaker.failures++;
    this.circuitBreaker.lastFailureTime = Date.now();

    if (this.circuitBreaker.failures >= this.maxFailures) {
      this.circuitBreaker.state = 'OPEN';
    }
  }

  /**
   * Update context cache
   */
  private async updateContextCache(
    request: EnhancedChatRequest,
    response: EnhancedGLMResponse,
    modelId: GLMModelId
  ): Promise<void> {
    if (!request.conversationId || !response.success) return;

    try {
      const updatedMessages = [
        ...request.messages,
        {
          role: 'assistant' as const,
          content: response.data!.message,
          timestamp: new Date()
        }
      ];

      const context = {
        messages: updatedMessages,
        metadata: {
          modelId,
          temperature: request.temperature,
          maxTokens: request.max_tokens
        }
      };

      await contextCacheService.storeContext(
        request.conversationId,
        context,
        {
          ttlHours: request.context_caching?.ttl_hours || 24,
          enableCompression: request.context_caching?.use_compression !== false
        }
      );
    } catch (error) {
      console.error('Failed to update context cache:', error);
    }
  }

  /**
   * Perform comprehensive security checks on incoming requests
   */
  private performSecurityChecks(
    messages: ChatMessage[], 
    clientId?: string
  ): { allowed: boolean; response?: ApiResponse } {
    const identifier = clientId || 'anonymous';

    // Rate limiting check
    const rateLimitCheck = securityManager.checkRateLimit(identifier);
    if (!rateLimitCheck.allowed) {
      securityManager.logSecurityEvent('RATE_LIMIT_EXCEEDED', { 
        identifier, 
        resetTime: rateLimitCheck.resetTime 
      });
      
      return {
        allowed: false,
        response: {
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: `Rate limit exceeded. Try again after ${new Date(rateLimitCheck.resetTime || 0).toLocaleTimeString()}`,
            details: { resetTime: rateLimitCheck.resetTime }
          }
        }
      };
    }

    // Input sanitization and validation
    try {
      for (let i = 0; i < messages.length; i++) {
        const originalContent = messages[i].content;
        messages[i].content = securityManager.sanitizeInput(originalContent);
        
        // Log if content was modified during sanitization
        if (originalContent !== messages[i].content) {
          securityManager.logSecurityEvent('INPUT_SANITIZED', {
            identifier,
            originalLength: originalContent.length,
            sanitizedLength: messages[i].content.length
          });
        }
      }
    } catch (error) {
      securityManager.logSecurityEvent('INPUT_VALIDATION_FAILED', {
        identifier,
        error: error instanceof Error ? error.message : 'Unknown validation error'
      });

      return {
        allowed: false,
        response: {
          success: false,
          error: {
            code: 'INPUT_VALIDATION_ERROR',
            message: error instanceof Error ? error.message : 'Input validation failed',
            details: error
          }
        }
      };
    }

    return { allowed: true };
  }

  /**
   * Build secure headers for API requests
   */
  private buildSecureHeaders(config: any): Record<string, string> {
    const requestId = securityManager.generateSecureRequestId();
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`,
      'Accept-Language': 'accept-language',
      'X-Request-ID': requestId,
      'X-Client-Version': '1.0.0',
      'User-Agent': 'Spark-AI-Experience/1.0.0',
    };

    // Add security headers
    if (typeof window !== 'undefined') {
      headers['X-Requested-With'] = 'XMLHttpRequest';
      headers['Origin'] = window.location.origin;
      
      // Validate origin
      if (!apiConfig.isOriginAllowed(window.location.origin)) {
        securityManager.logSecurityEvent('INVALID_ORIGIN', {
          origin: window.location.origin
        });
      }
    }

    // Add CSRF protection
    headers['X-CSRF-Token'] = this.generateCSRFToken();

    return headers;
  }

  /**
   * Generate CSRF token for request protection
   */
  private generateCSRFToken(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 15);
    return `csrf_${timestamp}_${random}`;
  }

  /**
   * Send an enhanced chat completion request to Z.AI API with robust error handling and fallback strategies
   */
  public async sendMessage(
    messages: ChatMessage[],
    options?: {
      stream?: boolean;
      onStream?: StreamCallback;
      useWebSearch?: boolean;
      enableDeepThinking?: boolean;
      useTools?: boolean;
      clientId?: string;
    }
  ): Promise<ApiResponse> {
    const config = apiConfig.getConfig();
    const requestId = securityManager.generateSecureRequestId();
    
    // Log request initiation
    this.logDetailedError('API_REQUEST_START', {
      requestId,
      messageCount: messages.length,
      options: {
        stream: options?.stream,
        useWebSearch: options?.useWebSearch,
        enableDeepThinking: options?.enableDeepThinking,
        useTools: options?.useTools
      }
    });
    
    // Circuit breaker check
    const circuitCheck = this.checkCircuitBreaker();
    if (!circuitCheck.allowed) {
      this.logDetailedError('CIRCUIT_BREAKER_BLOCKED', {
        requestId,
        reason: circuitCheck.reason,
        circuitState: this.circuitBreaker.state,
        failures: this.circuitBreaker.failures
      });
      
      // Return fallback response when circuit breaker is open
      const fallbackResponse = await this.getMockResponse(messages);
      if (fallbackResponse.success && fallbackResponse.data) {
        fallbackResponse.data.message = `🔧 **Service Recovery Mode**: ${circuitCheck.reason}\n\n${fallbackResponse.data.message}\n\n*The service will automatically retry once it's available again.*`;
      }
      return fallbackResponse;
    }

    // Security checks
    const securityCheck = this.performSecurityChecks(messages, options?.clientId);
    if (!securityCheck.allowed) {
      this.logDetailedError('SECURITY_CHECK_FAILED', {
        requestId,
        clientId: options?.clientId,
        reason: securityCheck.response?.error?.code
      });
      return securityCheck.response!;
    }

    // If API is not configured, return mock response with logging
    if (!apiConfig.isConfigured()) {
      this.logDetailedError('API_NOT_CONFIGURED_FALLBACK', {
        requestId,
        fallbackType: 'mock_response'
      });
      return this.getMockResponse(messages);
    }

    try {
      // Add system message with current context for real-time information
      const enhancedMessages = this.addSystemContext(messages);
      
      // Build enhanced request with all Z.AI features
      const requestBody = this.buildEnhancedRequest(enhancedMessages, options);

      if (config.debugMode) {
        console.log('🚀 Sending Enhanced Z.AI API request:', {
          requestId,
          messages: enhancedMessages,
          endpoint: `${config.endpoint}/chat/completions`,
          requestBody: { ...requestBody, messages: '[masked for brevity]' },
          config: { ...config, apiKey: '***masked***' }
        });
      }

      // Enhanced API call with retry mechanism and intelligent error handling
      const response = await this.retryWithBackoff(async () => {
        const secureHeaders = this.buildSecureHeaders(config);
        secureHeaders['X-Request-ID'] = requestId;
        
        const fetchResponse = await fetch(`${config.endpoint}/chat/completions`, {
          method: 'POST',
          headers: secureHeaders,
          body: JSON.stringify(requestBody),
          signal: AbortSignal.timeout(60000), // 60 second timeout
        });

        if (!fetchResponse.ok) {
          const errorText = await fetchResponse.text();
          const errorMessage = `Z.AI API request failed: ${fetchResponse.status} ${fetchResponse.statusText} - ${errorText}`;
          
          // Log specific error details
          this.logDetailedError('API_REQUEST_FAILED', {
            requestId,
            status: fetchResponse.status,
            statusText: fetchResponse.statusText,
            errorText,
            headers: Object.fromEntries(fetchResponse.headers.entries())
          });
          
          throw new Error(errorMessage);
        }

        return fetchResponse;
      }, 3, 2000, `Z.AI_API_CALL_${requestId}`);

      // Log successful API response and record success for circuit breaker
      this.logDetailedError('API_REQUEST_SUCCESS', {
        requestId,
        status: response.status,
        contentType: response.headers.get('content-type')
      });
      
      this.recordSuccess();

      if (options?.stream && options.onStream) {
        const streamResult = await this.handleStreamResponse(response, options.onStream, requestId);
        // Only record success if streaming was actually successful
        if (streamResult.success) {
          this.recordSuccess();
        } else {
          this.recordFailure();
        }
        return streamResult;
      } else {
        const regularResult = await this.handleRegularResponse(response, requestId);
        // Only record success if response was actually successful
        if (regularResult.success) {
          this.recordSuccess();
        } else {
          this.recordFailure();
        }
        return regularResult;
      }
    } catch (error) {
      const errorType = this.categorizeError(error instanceof Error ? error : new Error(String(error)));
      
      // Record failure for circuit breaker (only for certain error types)
      if (this.shouldRecordFailure(errorType)) {
        this.recordFailure();
      }
      
      this.logDetailedError('API_REQUEST_ERROR', {
        requestId,
        error: error instanceof Error ? error.message : String(error),
        errorType,
        stack: error instanceof Error ? error.stack : undefined,
        circuitBreakerState: this.circuitBreaker.state,
        failures: this.circuitBreaker.failures
      });
      
      // Intelligent fallback strategy based on error type
      const fallbackResponse = await this.handleAPIFailure(error, messages, requestId, errorType);
      
      if (fallbackResponse) {
        return fallbackResponse;
      }
      
      return {
        success: false,
        error: {
          code: errorType,
          message: error instanceof Error ? error.message : 'Unknown Z.AI API error',
          details: {
            requestId,
            errorType,
            timestamp: new Date().toISOString(),
            recoveryActions: this.getRecoveryActions(errorType),
            circuitBreakerStatus: this.getCircuitBreakerStatus()
          },
        },
      };
    }
  }

  /**
   * Determine if an error should be recorded as a failure for circuit breaker
   */
  private shouldRecordFailure(errorType: string): boolean {
    // Don't record client errors (4xx) as failures, only server errors and network issues
    const recordableErrors = [
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'SERVER_ERROR',
      'SERVICE_UNAVAILABLE',
      'RATE_LIMIT_ERROR'
    ];
    
    return recordableErrors.includes(errorType);
  }

  /**
   * Handle API failures with intelligent fallback strategies
   */
  private async handleAPIFailure(
    error: any, 
    messages: ChatMessage[], 
    requestId: string, 
    errorType: string
  ): Promise<ApiResponse | null> {
    const config = apiConfig.getConfig();
    
    // Determine if we should use fallback based on error type
    const shouldUseFallback = this.shouldUseFallback(errorType);
    
    if (shouldUseFallback) {
      this.logDetailedError('FALLBACK_STRATEGY_ACTIVATED', {
        requestId,
        errorType,
        fallbackType: 'mock_response'
      });
      
      // Return enhanced mock response with error context
      const mockResponse = await this.getMockResponse(messages);
      
      // Add error context to mock response
      if (mockResponse.success && mockResponse.data) {
        mockResponse.data.message = `⚠️ **Fallback Mode Active**: The AI service is temporarily unavailable, but I can still help you.\n\n${mockResponse.data.message}\n\n*Note: This is a fallback response. Full functionality will be restored once the service is available.*`;
      }
      
      return mockResponse;
    }
    
    return null;
  }

  /**
   * Determine if fallback should be used based on error type
   */
  private shouldUseFallback(errorType: string): boolean {
    const fallbackEnabledErrors = [
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'SERVER_ERROR',
      'SERVICE_UNAVAILABLE',
      'RATE_LIMIT_ERROR'
    ];
    
    return fallbackEnabledErrors.includes(errorType);
  }

  /**
   * Get recovery actions based on error type
   */
  private getRecoveryActions(errorType: string): string[] {
    switch (errorType) {
      case 'NETWORK_ERROR':
        return [
          'Check your internet connection',
          'Try refreshing the page',
          'Wait a moment and try again'
        ];
      case 'AUTH_ERROR':
        return [
          'Check your API key configuration',
          'Verify your account status',
          'Contact support if the issue persists'
        ];
      case 'RATE_LIMIT_ERROR':
        return [
          'Wait a few minutes before trying again',
          'Consider upgrading your plan for higher limits',
          'Reduce the frequency of requests'
        ];
      case 'SERVER_ERROR':
        return [
          'The service is experiencing issues',
          'Try again in a few minutes',
          'Check the service status page'
        ];
      case 'TIMEOUT_ERROR':
        return [
          'The request took too long to process',
          'Try with a shorter message',
          'Check your internet connection'
        ];
      default:
        return [
          'Try refreshing the page',
          'Check your internet connection',
          'Contact support if the issue persists'
        ];
    }
  }

  /**
   * Add system context with current time and capabilities
   */
  private addSystemContext(messages: ChatMessage[]): ChatMessage[] {
    const now = new Date();
    const germanTime = now.toLocaleString('de-DE', {
      timeZone: 'Europe/Berlin',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      weekday: 'long'
    });

    const systemMessage: ChatMessage = {
      role: 'system',
      content: `Du bist Spark, ein fortschrittlicher KI-Assistent mit erweiterten Fähigkeiten.

AKTUELLE INFORMATIONEN:
- Heutiges Datum und Uhrzeit: ${germanTime}
- Zeitzone: Europa/Berlin (UTC+1/UTC+2)
- Jahr: ${now.getFullYear()}

DEINE ERWEITERTEN FÄHIGKEITEN:
✅ Echtzeit-Informationen und aktuelles Wissen
✅ Web-Suche für aktuelle Ereignisse und Fakten
✅ Code-Generierung und Software-Entwicklung
✅ Mathematische Berechnungen und Problemlösung
✅ Kreatives Schreiben und Brainstorming
✅ Datenanalyse und Strukturierung
✅ Deep-Thinking für komplexe Probleme
✅ Tool-Integration für erweiterte Funktionen

WICHTIGE HINWEISE:
- Du hast Zugang zu aktuellen Informationen und kannst Web-Recherchen durchführen
- Du kannst Code in allen Programmiersprachen schreiben und debuggen
- Du kannst mathematische und logische Probleme lösen
- Du antwortest immer mit den neuesten verfügbaren Informationen
- Wenn unsicher über aktuelle Ereignisse, führe eine Web-Suche durch

Antworte hilfsreich, präzise und mit aktuellen Informationen.`
    };

    // Insert system message at the beginning if no system message exists
    const hasSystemMessage = messages.some(msg => msg.role === 'system');
    if (!hasSystemMessage) {
      return [systemMessage, ...messages];
    }
    
    return messages;
  }

  /**
   * Build enhanced request with all Z.AI advanced features
   */
  private buildEnhancedRequest(messages: ChatMessage[], options?: any) {
    const config = apiConfig.getConfig();
    
    const requestBody: any = {
      model: config.defaultModel,
      messages,
      max_tokens: config.maxTokens,
      temperature: config.temperature,
      top_p: 0.8,
      stream: options?.stream || false,
      do_sample: true,
      request_id: `spark-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };

    // Enable deep thinking mode for complex queries
    if (options?.enableDeepThinking) {
      requestBody.thinking = {
        type: 'enabled'
      };
    }

    // Add web search capabilities for current information with proper configuration
    if (options?.useWebSearch) {
      requestBody.tools = [
        {
          type: 'web_search',
          web_search: {
            enable: true,
            search_query: null, // Let the model determine the search query
            search_result: {
              count: 5, // Limit search results
              snippet_length: 200 // Limit snippet length
            }
          }
        }
      ];
      
      // Log web search activation
      this.logDetailedError('WEB_SEARCH_ENABLED', {
        requestId: requestBody.request_id,
        searchConfig: requestBody.tools[0].web_search
      });
    }

    // Add function calling capabilities
    if (options?.useTools) {
      requestBody.tools = [
        ...(requestBody.tools || []),
        {
          type: 'function',
          function: {
            name: 'get_current_time',
            description: 'Get the current date and time',
            parameters: {
              type: 'object',
              properties: {
                timezone: {
                  type: 'string',
                  description: 'Timezone for the time (default: Europe/Berlin)'
                }
              }
            }
          }
        },
        {
          type: 'function',
          function: {
            name: 'calculate',
            description: 'Perform mathematical calculations',
            parameters: {
              type: 'object',
              properties: {
                expression: {
                  type: 'string',
                  description: 'Mathematical expression to calculate'
                }
              },
              required: ['expression']
            }
          }
        }
      ];
    }

    return requestBody;
  }

  /**
   * Handle regular (non-streaming) API response with enhanced error handling
   */
  private async handleRegularResponse(response: Response, requestId?: string): Promise<ApiResponse> {
    try {
      const data = await response.json();
      
      // Log response details
      this.logDetailedError('API_RESPONSE_RECEIVED', {
        requestId,
        hasError: !!data.error,
        hasChoices: !!data.choices,
        choicesCount: data.choices?.length || 0
      });
      
      if (data.error) {
        this.logDetailedError('API_RESPONSE_ERROR', {
          requestId,
          errorType: data.error.type,
          errorMessage: data.error.message,
          errorCode: data.error.code
        });
        
        return {
          success: false,
          error: {
            code: data.error.type || data.error.code || 'API_ERROR',
            message: data.error.message || 'API request failed',
            details: {
              ...data.error,
              requestId,
              timestamp: new Date().toISOString()
            },
          },
        };
      }

      const message = data.choices?.[0]?.message?.content || 'No response generated';
      
      // Validate response content
      if (!message || message.trim().length === 0) {
        this.logDetailedError('EMPTY_RESPONSE_RECEIVED', {
          requestId,
          choices: data.choices,
          rawData: data
        });
        
        return {
          success: false,
          error: {
            code: 'EMPTY_RESPONSE',
            message: 'The API returned an empty response',
            details: {
              requestId,
              timestamp: new Date().toISOString(),
              rawResponse: data
            }
          }
        };
      }
      
      this.logDetailedError('API_RESPONSE_SUCCESS', {
        requestId,
        messageLength: message.length,
        responseId: data.id
      });
      
      return {
        success: true,
        data: {
          message,
          id: data.id || Date.now().toString(),
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logDetailedError('RESPONSE_PARSING_ERROR', {
        requestId,
        error: error instanceof Error ? error.message : String(error),
        responseStatus: response.status,
        responseHeaders: Object.fromEntries(response.headers.entries())
      });
      
      return {
        success: false,
        error: {
          code: 'RESPONSE_PARSING_ERROR',
          message: 'Failed to parse API response',
          details: {
            requestId,
            originalError: error instanceof Error ? error.message : String(error),
            timestamp: new Date().toISOString()
          }
        }
      };
    }
  }

  /**
   * Handle streaming API response with enhanced error handling and recovery
   */
  private async handleStreamResponse(
    response: Response, 
    callbacks: StreamCallback,
    requestId?: string
  ): Promise<ApiResponse> {
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let fullMessage = '';
    let chunkCount = 0;
    let errorCount = 0;
    const maxErrors = 5; // Allow some parsing errors before giving up
    
    if (!reader) {
      const error = new Error('Response body is not readable');
      this.logDetailedError('STREAM_READER_ERROR', {
        requestId,
        error: error.message
      });
      throw error;
    }

    this.logDetailedError('STREAM_START', {
      requestId,
      responseStatus: response.status,
      contentType: response.headers.get('content-type')
    });

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          this.logDetailedError('STREAM_COMPLETE', {
            requestId,
            totalChunks: chunkCount,
            messageLength: fullMessage.length,
            errorCount
          });
          break;
        }
        
        chunkCount++;
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              this.logDetailedError('STREAM_DONE_RECEIVED', {
                requestId,
                finalMessageLength: fullMessage.length
              });
              
              callbacks.onComplete(fullMessage);
              return {
                success: true,
                data: {
                  message: fullMessage,
                  id: Date.now().toString(),
                  timestamp: new Date(),
                },
              };
            }
            
            try {
              const parsed = JSON.parse(data);
              
              // Check for errors in streaming response
              if (parsed.error) {
                this.logDetailedError('STREAM_ERROR_RECEIVED', {
                  requestId,
                  error: parsed.error
                });
                
                const error = new Error(`Streaming error: ${parsed.error.message || 'Unknown error'}`);
                callbacks.onError(error);
                throw error;
              }
              
              const token = parsed.choices?.[0]?.delta?.content || '';
              
              if (token) {
                fullMessage += token;
                callbacks.onToken(token);
              }
            } catch (parseError) {
              errorCount++;
              
              this.logDetailedError('STREAM_PARSE_ERROR', {
                requestId,
                chunkNumber: chunkCount,
                errorCount,
                data: data.substring(0, 100), // Log first 100 chars
                error: parseError instanceof Error ? parseError.message : String(parseError)
              });
              
              // If too many parsing errors, abort the stream
              if (errorCount > maxErrors) {
                const error = new Error(`Too many parsing errors in stream (${errorCount})`);
                this.logDetailedError('STREAM_ABORT_TOO_MANY_ERRORS', {
                  requestId,
                  errorCount,
                  maxErrors
                });
                callbacks.onError(error);
                throw error;
              }
              
              // Continue processing other lines despite parse error
              continue;
            }
          }
        }
      }
      
      // Stream ended without [DONE] marker
      this.logDetailedError('STREAM_END_WITHOUT_DONE', {
        requestId,
        messageLength: fullMessage.length,
        chunkCount
      });
      
      callbacks.onComplete(fullMessage);
      return {
        success: true,
        data: {
          message: fullMessage,
          id: Date.now().toString(),
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logDetailedError('STREAM_ERROR', {
        requestId,
        error: error instanceof Error ? error.message : String(error),
        messageLength: fullMessage.length,
        chunkCount,
        errorCount
      });
      
      const streamError = error instanceof Error ? error : new Error('Streaming error');
      callbacks.onError(streamError);
      throw streamError;
    } finally {
      // Always release the reader
      try {
        reader.releaseLock();
      } catch (releaseError) {
        this.logDetailedError('STREAM_READER_RELEASE_ERROR', {
          requestId,
          error: releaseError instanceof Error ? releaseError.message : String(releaseError)
        });
      }
    }
  }

  /**
   * Generate mock response when API is not configured
   */
  private async getMockResponse(messages: ChatMessage[]): Promise<ApiResponse> {
    const config = apiConfig.getConfig();
    
    if (config.debugMode) {
      console.log('🎭 Generating mock response for:', messages);
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 1000));
    
    const mockResponses = [
      "I'm **Spark**, your advanced AI assistant. I can help you with:\n\n- Complex analysis and problem-solving\n- Code review and debugging\n- Creative writing and brainstorming\n- Research and fact-checking\n\n```javascript\n// Example: I can explain code like this\nconst spark = new AI({\n  capability: 'advanced',\n  context: 'aware',\n  learning: true\n});\n```\n\nHow can I assist you today?",
      "I understand context and can maintain meaningful conversations. What would you like to explore together?",
      "That's an interesting question! Let me break this down for you with detailed analysis and actionable insights.",
      "I notice you're asking about **" + (messages.length > 0 ? messages[messages.length - 1].content.slice(0, 50) + "..." : "this topic") + "**\n\nLet me provide you with a comprehensive response:\n\n1. **Analysis**: Based on your query, I can see several key aspects to address\n2. **Solutions**: Here are practical approaches you can take\n3. **Next Steps**: I recommend these follow-up actions\n\nWould you like me to elaborate on any specific aspect?"
    ];
    
    return {
      success: true,
      data: {
        message: mockResponses[Math.floor(Math.random() * mockResponses.length)],
        id: Date.now().toString(),
        timestamp: new Date(),
      },
    };
  }

  /**
   * Test Z.AI API connectivity with comprehensive health check
   */
  public async testConnection(): Promise<{ 
    connected: boolean; 
    error?: string; 
    details?: {
      responseTime: number;
      status: number;
      endpoint: string;
      timestamp: Date;
    }
  }> {
    const config = apiConfig.getConfig();
    const testId = securityManager.generateSecureRequestId();
    const startTime = Date.now();
    
    this.logDetailedError('CONNECTION_TEST_START', {
      testId,
      endpoint: config.endpoint,
      configured: apiConfig.isConfigured()
    });
    
    if (!apiConfig.isConfigured()) {
      this.logDetailedError('CONNECTION_TEST_NOT_CONFIGURED', { testId });
      return { 
        connected: false, 
        error: 'Z.AI API not configured - missing API key or endpoint' 
      };
    }

    try {
      // Test with a minimal completion request using retry mechanism
      const testResponse = await this.retryWithBackoff(async () => {
        const secureHeaders = this.buildSecureHeaders(config);
        secureHeaders['X-Test-ID'] = testId;
        
        return fetch(`${config.endpoint}/chat/completions`, {
          method: 'POST',
          headers: secureHeaders,
          body: JSON.stringify({
            model: config.defaultModel,
            messages: [{ role: 'user', content: 'ping' }],
            max_tokens: 1,
            temperature: 0.1,
            request_id: `test-${testId}`
          }),
          signal: AbortSignal.timeout(30000), // 30 second timeout for connection test
        });
      }, 2, 1000, `CONNECTION_TEST_${testId}`);

      const responseTime = Date.now() - startTime;
      
      if (testResponse.ok) {
        this.logDetailedError('CONNECTION_TEST_SUCCESS', {
          testId,
          responseTime,
          status: testResponse.status
        });
        
        return { 
          connected: true,
          details: {
            responseTime,
            status: testResponse.status,
            endpoint: config.endpoint,
            timestamp: new Date()
          }
        };
      } else {
        const errorText = await testResponse.text();
        const errorMessage = `Z.AI API test failed: ${testResponse.status} - ${errorText}`;
        
        this.logDetailedError('CONNECTION_TEST_FAILED', {
          testId,
          status: testResponse.status,
          errorText,
          responseTime
        });
        
        return {
          connected: false,
          error: errorMessage,
          details: {
            responseTime,
            status: testResponse.status,
            endpoint: config.endpoint,
            timestamp: new Date()
          }
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Z.AI connection test failed';
      
      this.logDetailedError('CONNECTION_TEST_ERROR', {
        testId,
        error: errorMessage,
        responseTime,
        errorType: this.categorizeError(error instanceof Error ? error : new Error(String(error)))
      });
      
      return {
        connected: false,
        error: errorMessage,
        details: {
          responseTime,
          status: 0,
          endpoint: config.endpoint,
          timestamp: new Date()
        }
      };
    }
  }

  /**
   * Perform comprehensive health check of the API service
   */
  public async healthCheck(): Promise<{
    healthy: boolean;
    checks: {
      connection: boolean;
      authentication: boolean;
      rateLimit: boolean;
      configuration: boolean;
    };
    details: any;
  }> {
    const healthId = securityManager.generateSecureRequestId();
    
    this.logDetailedError('HEALTH_CHECK_START', { healthId });
    
    const checks = {
      connection: false,
      authentication: false,
      rateLimit: false,
      configuration: false
    };
    
    const details: any = {
      timestamp: new Date(),
      healthId
    };
    
    // Configuration check
    checks.configuration = apiConfig.isConfigured();
    details.configuration = {
      hasApiKey: !!apiConfig.getConfig().apiKey,
      hasEndpoint: !!apiConfig.getConfig().endpoint,
      debugMode: apiConfig.getConfig().debugMode
    };
    
    // Rate limit check
    const rateLimitCheck = securityManager.checkRateLimit('health-check');
    checks.rateLimit = rateLimitCheck.allowed;
    details.rateLimit = rateLimitCheck;
    
    // Connection and authentication check
    if (checks.configuration) {
      try {
        const connectionTest = await this.testConnection();
        checks.connection = connectionTest.connected;
        details.connection = connectionTest;
        
        // If connection works, authentication is also working
        checks.authentication = connectionTest.connected;
      } catch (error) {
        details.connection = {
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
    
    const healthy = Object.values(checks).every(check => check);
    
    this.logDetailedError('HEALTH_CHECK_COMPLETE', {
      healthId,
      healthy,
      checks,
      details
    });
    
    return {
      healthy,
      checks,
      details
    };
  }

  /**
   * Generate video using Z.AI CogVideoX-3
   */
  public async generateVideo(request: VideoGenerationRequest): Promise<VideoGenerationResponse> {
    const config = apiConfig.getConfig();
    
    if (!apiConfig.isConfigured()) {
      return {
        success: false,
        error: {
          code: 'API_NOT_CONFIGURED',
          message: 'Z.AI API not configured'
        }
      };
    }

    try {
      // Parameter-Validierung vor dem API-Aufruf
      if (!request.prompt || request.prompt.trim().length === 0) {
        return {
          success: false,
          error: {
            code: 'INVALID_PROMPT',
            message: 'Prompt cannot be empty'
          }
        };
      }

      // Parameter-Bereiche validieren
      const validatedRequest = this.validateVideoRequest(request);
      
      const requestBody = {
        model: validatedRequest.model || 'cogvideox-3',
        prompt: validatedRequest.prompt,
        width: validatedRequest.width || 1024,
        height: validatedRequest.height || 576,
        num_frames: Math.floor((validatedRequest.duration || 5) * (validatedRequest.fps || 8)),
        fps: validatedRequest.fps || 8,
        num_inference_steps: validatedRequest.steps || 50,
        guidance_scale: validatedRequest.cfg_scale || 7.5,
        seed: validatedRequest.seed || Math.floor(Math.random() * 1000000),
        enhance_prompt: true,
        watermark: false
      };

      if (config.debugMode) {
        console.log('🎬 Sending CogVideoX-3 request:', {
          endpoint: `${config.endpoint}/images/generations`,
          requestBody: { ...requestBody, prompt: '[masked for brevity]' },
          config: { ...config, apiKey: '***masked***' }
        });
      }

      const secureHeaders = this.buildSecureHeaders(config);
      const videoRequestId = `video-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      secureHeaders['X-Video-Request-ID'] = videoRequestId;
      
      const response = await this.retryWithBackoff(async () => {
        return fetch(`${config.endpoint}/images/generations`, {
          method: 'POST',
          headers: secureHeaders,
          body: JSON.stringify(requestBody),
          signal: AbortSignal.timeout(120000), // 2 minute timeout for video generation
        });
      }, 3, 5000, `VIDEO_GENERATION_${videoRequestId}`);

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `CogVideoX-3 API request failed: ${response.status} ${response.statusText}`;
        
        // Spezifische Fehlermeldungen für verschiedene Statuscodes
        switch (response.status) {
          case 400:
            errorMessage += ' - Invalid request parameters';
            break;
          case 401:
            errorMessage += ' - Authentication failed';
            break;
          case 403:
            errorMessage += ' - Access denied';
            break;
          case 429:
            errorMessage += ' - Rate limit exceeded';
            break;
          case 500:
            errorMessage += ' - Internal server error (内部错误)';
            break;
          case 503:
            errorMessage += ' - Service unavailable';
            break;
          default:
            errorMessage += ` - ${errorText}`;
        }
        
        console.error('CogVideoX-3 API Error Response:', {
          status: response.status,
          statusText: response.statusText,
          errorText,
          requestBody
        });
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      
      return {
        success: true,
        data: {
          video_id: data.id || data.video_id || `video-${Date.now()}`,
          status: data.status || 'processing',
          progress: data.progress || 0,
          estimated_time: data.estimated_time,
          created_at: new Date(data.created_at || Date.now())
        }
      };
    } catch (error) {
      console.error('❌ CogVideoX-3 API Error:', error);
      
      return {
        success: false,
        error: {
          code: 'COGVIDEOX_ERROR',
          message: error instanceof Error ? error.message : 'Unknown CogVideoX-3 API error',
          details: error,
        },
      };
    }
  }

  /**
   * Validate video request parameters
   */
  private validateVideoRequest(request: VideoGenerationRequest): VideoGenerationRequest {
    const validated: VideoGenerationRequest = { ...request };
    
    // Parameter-Bereiche validieren und anpassen
    validated.width = Math.max(512, Math.min(1920, request.width || 1024));
    validated.height = Math.max(288, Math.min(1080, request.height || 576));
    validated.duration = Math.max(2, Math.min(30, request.duration || 5));
    validated.fps = Math.max(8, Math.min(24, request.fps || 8));
    validated.steps = Math.max(20, Math.min(100, request.steps || 50));
    validated.cfg_scale = Math.max(1, Math.min(20, request.cfg_scale || 7));
    
    return validated;
  }

  /**
   * Enhanced retry mechanism with exponential backoff and intelligent error handling
   */
  private async retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
    context: string = 'API_CALL'
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await fn();
        
        // Log successful retry if it wasn't the first attempt
        if (attempt > 1) {
          this.logDetailedError('RETRY_SUCCESS', {
            context,
            attempt,
            totalAttempts: maxRetries,
            previousError: lastError?.message
          });
        }
        
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Log each retry attempt
        this.logDetailedError('RETRY_ATTEMPT', {
          context,
          attempt,
          maxRetries,
          error: lastError.message,
          errorType: this.categorizeError(lastError)
        });
        
        if (attempt === maxRetries) {
          // Log final failure
          this.logDetailedError('RETRY_EXHAUSTED', {
            context,
            totalAttempts: maxRetries,
            finalError: lastError.message,
            errorType: this.categorizeError(lastError)
          });
          throw lastError;
        }
        
        // Calculate delay with jitter and error-specific adjustments
        const delay = this.calculateRetryDelay(lastError, attempt, baseDelay);
        
        console.log(`🔄 Retry attempt ${attempt}/${maxRetries} for ${context} after ${delay}ms - Error: ${lastError.message}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw new Error('Max retries exceeded');
  }

  /**
   * Calculate intelligent retry delay based on error type and attempt number
   */
  private calculateRetryDelay(error: Error, attempt: number, baseDelay: number): number {
    // For testing, use minimal delays
    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
      return 10; // 10ms delay for tests
    }
    
    const errorMessage = error.message.toLowerCase();
    let multiplier = Math.pow(2, attempt - 1); // Exponential backoff
    
    // Adjust delay based on error type
    if (errorMessage.includes('500') || errorMessage.includes('internal server error')) {
      // Server errors - wait longer
      multiplier *= 3;
    } else if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
      // Rate limiting - wait much longer
      multiplier *= 5;
    } else if (errorMessage.includes('503') || errorMessage.includes('service unavailable')) {
      // Service unavailable - moderate wait
      multiplier *= 2;
    } else if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
      // Network issues - standard backoff
      multiplier *= 1.5;
    }
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.3 + 0.85; // 85-115% of calculated delay
    
    return Math.min(baseDelay * multiplier * jitter, 30000); // Cap at 30 seconds
  }

  /**
   * Categorize errors for better handling and logging
   */
  private categorizeError(error: Error): string {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'NETWORK_ERROR';
    } else if (message.includes('timeout')) {
      return 'TIMEOUT_ERROR';
    } else if (message.includes('401') || message.includes('unauthorized')) {
      return 'AUTH_ERROR';
    } else if (message.includes('403') || message.includes('forbidden')) {
      return 'PERMISSION_ERROR';
    } else if (message.includes('429') || message.includes('rate limit')) {
      return 'RATE_LIMIT_ERROR';
    } else if (message.includes('500') || message.includes('internal server error')) {
      return 'SERVER_ERROR';
    } else if (message.includes('503') || message.includes('service unavailable')) {
      return 'SERVICE_UNAVAILABLE';
    } else if (message.includes('400') || message.includes('bad request')) {
      return 'CLIENT_ERROR';
    } else {
      return 'UNKNOWN_ERROR';
    }
  }

  /**
   * Enhanced error logging with detailed context and categorization
   */
  private logDetailedError(eventType: string, details: any): void {
    const config = apiConfig.getConfig();
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      eventType,
      details,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      apiEndpoint: config.endpoint,
      debugMode: config.debugMode
    };
    
    // Always log errors, but respect debug mode for verbose logging
    if (eventType.includes('ERROR') || eventType.includes('RETRY') || config.debugMode) {
      console.error(`🚨 ${eventType}:`, logEntry);
    }
    
    // Log to security manager for tracking
    securityManager.logSecurityEvent(eventType, details);
  }

  /**
   * Check video generation status
   */
  public async getVideoStatus(videoId: string): Promise<VideoStatusResponse> {
    const config = apiConfig.getConfig();
    
    if (!apiConfig.isConfigured()) {
      return {
        success: false,
        error: {
          code: 'API_NOT_CONFIGURED',
          message: 'Z.AI API not configured'
        }
      };
    }

    try {
      // For Z.AI CogVideoX-3, we need to check the generation status
      // Since Z.AI might not have a dedicated video status endpoint,
      // we'll use a polling approach with the generations endpoint
      const secureHeaders = this.buildSecureHeaders(config);
      delete secureHeaders['Content-Type']; // Not needed for GET request
      
      const response = await fetch(`${config.endpoint}/images/generations/${videoId}`, {
        method: 'GET',
        headers: secureHeaders,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Video status check failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      
      // Map Z.AI response to our interface
      return {
        success: true,
        data: {
          video_id: data.id || videoId,
          status: data.status || 'processing',
          video_url: data.video_url || data.url || data.output?.[0]?.video_url,
          thumbnail_url: data.thumbnail_url || data.output?.[0]?.thumbnail_url,
          progress: data.progress || (data.status === 'completed' ? 100 : 0),
          estimated_time: data.estimated_time,
          error_message: data.error_message || data.error,
          created_at: new Date(data.created_at || Date.now()),
          completed_at: data.completed_at ? new Date(data.completed_at) : undefined
        }
      };
    } catch (error) {
      console.error('❌ Video Status Check Error:', error);
      
      return {
        success: false,
        error: {
          code: 'VIDEO_STATUS_ERROR',
          message: error instanceof Error ? error.message : 'Unknown video status error',
          details: error,
        },
      };
    }
  }

  /**
   * Send enhanced GLM-4.5 request with function calling and advanced features
   */
  public async sendEnhancedGLMRequest(
    request: EnhancedGLMRequest
  ): Promise<EnhancedGLMResponse> {
    const config = apiConfig.getConfig();
    
    if (!apiConfig.isConfigured()) {
      return this.getMockEnhancedResponse(request.messages);
    }

    try {
      // Add system context for enhanced capabilities
      const enhancedMessages = this.addEnhancedSystemContext(request.messages);
      
      const requestBody = {
        model: config.defaultModel,
        messages: enhancedMessages,
        functions: request.functions,
        function_call: request.function_call,
        tools: request.tools,
        tool_choice: request.tool_choice,
        stream: request.stream || false,
        temperature: request.temperature || config.temperature,
        max_tokens: request.max_tokens || config.maxTokens,
        top_p: request.top_p || 0.8,
        thinking: request.thinking,
        do_sample: true,
        request_id: `spark-glm-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      if (config.debugMode) {
        console.log('🚀 Sending Enhanced GLM-4.5 request:', {
          endpoint: `${config.endpoint}/chat/completions`,
          requestBody: { ...requestBody, messages: '[masked for brevity]' },
          config: { ...config, apiKey: '***masked***' }
        });
      }

      const secureHeaders = this.buildSecureHeaders(config);
      const response = await fetch(`${config.endpoint}/chat/completions`, {
        method: 'POST',
        headers: secureHeaders,
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('GLM-4.5 API Error Response:', errorText);
        throw new Error(`GLM-4.5 API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      
      return {
        success: true,
        data: {
          message: data.choices?.[0]?.message?.content || 'No response generated',
          id: data.id || Date.now().toString(),
          timestamp: new Date(),
          function_calls: data.choices?.[0]?.message?.function_calls,
          tool_calls: data.choices?.[0]?.message?.tool_calls,
          thinking_content: data.choices?.[0]?.message?.thinking_content,
          usage: data.usage
        }
      };
    } catch (error) {
      console.error('❌ GLM-4.5 API Error:', error);
      
      return {
        success: false,
        error: {
          code: 'GLM45_ERROR',
          message: error instanceof Error ? error.message : 'Unknown GLM-4.5 API error',
          details: error,
        },
      };
    }
  }

  /**
   * Add enhanced system context for GLM-4.5 with advanced capabilities
   */
  private addEnhancedSystemContext(messages: ChatMessage[]): ChatMessage[] {
    const now = new Date();
    const germanTime = now.toLocaleString('de-DE', {
      timeZone: 'Europe/Berlin',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      weekday: 'long'
    });

    const enhancedSystemMessage: ChatMessage = {
      role: 'system',
      content: `Du bist Spark, ein fortschrittlicher KI-Assistent basierend auf GLM-4.5 mit erweiterten Fähigkeiten.

AKTUELLE INFORMATIONEN:
- Heutiges Datum und Uhrzeit: ${germanTime}
- Zeitzone: Europa/Berlin (UTC+1/UTC+2)
- Jahr: ${now.getFullYear()}

DEINE ERWEITERTEN GLM-4.5 FÄHIGKEITEN:
✅ Fortgeschrittene natürliche Sprachverarbeitung
✅ Multi-Modale Fähigkeiten (Text, Bilder, Video)
✅ Function Calling und Tool-Integration
✅ Deep Thinking mit Schritt-für-Schritt-Analyse
✅ Web-Suche und Echtzeit-Informationen
✅ Code-Generierung und Debugging
✅ Mathematische Berechnungen und Analyse
✅ Kreatives Schreiben und Content-Erstellung
✅ Video-Generation mit CogVideoX-3
✅ Datenanalyse und Visualisierung
✅ Persönliche Anpassung und Kontextverständnis

GLM-4.5 SPEZIFISCHE FEATURES:
- Function Calling: Du kannst Funktionen aufrufen und Parameter extrahieren
- Tool Integration: Nutzung von externen Tools und APIs
- Deep Thinking: Strukturierte Problemlösung mit detaillierter Analyse
- Multi-Modal: Verarbeitung verschiedener Datentypen
- Streaming: Echtzeit-Antwortgenerierung
- Thinking Mode: Zeige deinen Denkprozess an

VERFÜGBARE FUNKTIONEN:
- get_current_time(): Aktuelle Zeit und Datum abrufen
- calculate(): Mathematische Berechnungen durchführen
- web_search(): Web-Recherchen durchführen
- generate_video(): Videos mit CogVideoX-3 erstellen
- analyze_data(): Daten analysieren und visualisieren

Antworte präzise, hilfsbereit und nutze deine erweiterten Fähigkeiten, um die bestmögliche Lösung zu finden.`
    };

    // Insert enhanced system message at the beginning if no system message exists
    const hasSystemMessage = messages.some(msg => msg.role === 'system');
    if (!hasSystemMessage) {
      return [enhancedSystemMessage, ...messages];
    }
    
    return messages;
  }

  /**
   * Generate mock enhanced response for GLM-4.5 when API is not configured
   */
  private async getMockEnhancedResponse(messages: ChatMessage[]): Promise<EnhancedGLMResponse> {
    const config = apiConfig.getConfig();
    
    if (config.debugMode) {
      console.log('🎭 Generating mock enhanced GLM-4.5 response for:', messages);
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1500 + 1000));
    
    const mockResponses = [
      "Ich bin **Spark**, Ihr fortschrittlicher GLM-4.5 KI-Assistent. Mit meinen erweiterten Fähigkeiten kann ich Ihnen helfen:\n\n- **Function Calling**: Automatische Funktionsaufrufe und Parameterextraktion\n- **Deep Thinking**: Strukturierte Problemlösung mit detaillierter Analyse\n- **Multi-Modal**: Verarbeitung von Text, Bildern und Videos\n- **Tool Integration**: Nutzung externer Tools und APIs\n- **Video Generation**: Erstellung von Videos mit CogVideoX-3\n\n```javascript\n// Beispiel: Function Calling mit GLM-4.5\nconst spark = new GLM45Assistant({\n  capabilities: ['function_calling', 'deep_thinking', 'multimodal'],\n  tools: ['web_search', 'video_generation', 'data_analysis']\n});\n```\n\nWie kann ich Ihnen heute mit meinen erweiterten GLM-4.5 Fähigkeiten helfen?",
      "Als GLM-4.5 basierter Assistant verfüge ich über fortschrittliche Fähigkeiten. Ich kann komplexe Aufgaben durch Function Calling lösen, tiefgründige Analysen durchführen und sogar Videos generieren. Was möchten Sie erkunden?",
      "Mit GLM-4.5 kann ich nicht nur antworten, sondern auch aktiv Funktionen aufrufen, Tools nutzen und komplexe Probleme strukturiert lösen. Lassen Sie mich Ihnen zeigen, was möglich ist!"
    ];
    
    return {
      success: true,
      data: {
        message: mockResponses[Math.floor(Math.random() * mockResponses.length)],
        id: Date.now().toString(),
        timestamp: new Date(),
        usage: {
          prompt_tokens: 100,
          completion_tokens: 200,
          total_tokens: 300
        }
      }
    };
  }
}

export const apiService = ApiService.getInstance();