/**
 * Subscription Service
 * Manages user subscriptions, features, and access validation
 */

import { PrismaClient } from '@prisma/client';
import {
  UserSubscription,
  SubscriptionTier,
  SubscriptionFeature,
  SUBSCRIPTION_TIERS,
  SubscriptionValidationError,
  ModelAccessError
} from '../types/glm-models';
import { modelRegistry } from '../config/glm-model-registry';

const prisma = new PrismaClient();

export interface CreateSubscriptionData {
  userId: string;
  tier: SubscriptionTier;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  currentPeriodStart?: Date;
  currentPeriodEnd?: Date;
  features?: Array<{
    featureKey: string;
    featureValue: string;
    enabled?: boolean;
  }>;
}

export interface UpdateSubscriptionData {
  tier?: SubscriptionTier;
  status?: 'active' | 'inactive' | 'expired' | 'cancelled';
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  currentPeriodStart?: Date;
  currentPeriodEnd?: Date;
  cancelAtPeriodEnd?: boolean;
}

export class SubscriptionService {
  /**
   * Get user subscription by user ID
   */
  async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    const subscription = await prisma.userSubscription.findUnique({
      where: { userId },
      include: {
        features: true,
        usage: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    if (!subscription) {
      return null;
    }

    return {
      id: subscription.id,
      userId: subscription.userId,
      tier: subscription.tier as SubscriptionTier,
      status: subscription.status as any,
      stripeCustomerId: subscription.stripeCustomerId || undefined,
      stripeSubscriptionId: subscription.stripeSubscriptionId || undefined,
      currentPeriodStart: subscription.currentPeriodStart || undefined,
      currentPeriodEnd: subscription.currentPeriodEnd || undefined,
      cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
      features: subscription.features.map(f => ({
        key: f.featureKey,
        value: f.featureValue,
        enabled: f.enabled
      })),
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt
    };
  }

  /**
   * Create a new subscription for a user
   */
  async createSubscription(data: CreateSubscriptionData): Promise<UserSubscription> {
    const existingSubscription = await this.getUserSubscription(data.userId);
    if (existingSubscription) {
      throw new SubscriptionValidationError(
        'User already has a subscription',
        data.tier,
        existingSubscription.tier
      );
    }

    const subscription = await prisma.userSubscription.create({
      data: {
        userId: data.userId,
        tier: data.tier,
        status: 'active',
        stripeCustomerId: data.stripeCustomerId,
        stripeSubscriptionId: data.stripeSubscriptionId,
        currentPeriodStart: data.currentPeriodStart,
        currentPeriodEnd: data.currentPeriodEnd,
        features: {
          create: data.features || this.getDefaultFeatures(data.tier)
        }
      },
      include: {
        features: true
      }
    });

    return {
      id: subscription.id,
      userId: subscription.userId,
      tier: subscription.tier as SubscriptionTier,
      status: subscription.status as any,
      stripeCustomerId: subscription.stripeCustomerId || undefined,
      stripeSubscriptionId: subscription.stripeSubscriptionId || undefined,
      currentPeriodStart: subscription.currentPeriodStart || undefined,
      currentPeriodEnd: subscription.currentPeriodEnd || undefined,
      cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
      features: subscription.features.map(f => ({
        key: f.featureKey,
        value: f.featureValue,
        enabled: f.enabled
      })),
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt
    };
  }

  /**
   * Update an existing subscription
   */
  async updateSubscription(
    userId: string,
    data: UpdateSubscriptionData
  ): Promise<UserSubscription> {
    const subscription = await prisma.userSubscription.update({
      where: { userId },
      data: {
        ...data,
        updatedAt: new Date()
      },
      include: {
        features: true
      }
    });

    return {
      id: subscription.id,
      userId: subscription.userId,
      tier: subscription.tier as SubscriptionTier,
      status: subscription.status as any,
      stripeCustomerId: subscription.stripeCustomerId || undefined,
      stripeSubscriptionId: subscription.stripeSubscriptionId || undefined,
      currentPeriodStart: subscription.currentPeriodStart || undefined,
      currentPeriodEnd: subscription.currentPeriodEnd || undefined,
      cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
      features: subscription.features.map(f => ({
        key: f.featureKey,
        value: f.featureValue,
        enabled: f.enabled
      })),
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt
    };
  }

  /**
   * Get or create a free subscription for a user
   */
  async getOrCreateFreeSubscription(userId: string): Promise<UserSubscription> {
    let subscription = await this.getUserSubscription(userId);
    
    if (!subscription) {
      subscription = await this.createSubscription({
        userId,
        tier: SUBSCRIPTION_TIERS.FREE
      });
    }

    return subscription;
  }

  /**
   * Validate if user has access to a specific model
   */
  async validateModelAccess(userId: string, modelId: string): Promise<boolean> {
    const subscription = await this.getOrCreateFreeSubscription(userId);
    
    if (subscription.status !== 'active') {
      return false;
    }

    return modelRegistry.validateModelAccess(modelId, subscription.tier);
  }

  /**
   * Get user's subscription tier
   */
  async getUserTier(userId: string): Promise<SubscriptionTier> {
    const subscription = await this.getOrCreateFreeSubscription(userId);
    return subscription.tier;
  }

  /**
   * Check if subscription is active
   */
  async isSubscriptionActive(userId: string): Promise<boolean> {
    const subscription = await this.getUserSubscription(userId);
    
    if (!subscription) {
      return false;
    }

    if (subscription.status !== 'active') {
      return false;
    }

    // Check if subscription has expired
    if (subscription.currentPeriodEnd && subscription.currentPeriodEnd < new Date()) {
      await this.updateSubscription(userId, { status: 'expired' });
      return false;
    }

    return true;
  }

  /**
   * Get available models for user
   */
  async getAvailableModels(userId: string) {
    const tier = await this.getUserTier(userId);
    return modelRegistry.getAvailableModels(tier);
  }

  /**
   * Get default model for user
   */
  async getDefaultModel(userId: string) {
    const tier = await this.getUserTier(userId);
    return modelRegistry.getDefaultModel(tier);
  }

  /**
   * Get default features for a subscription tier
   */
  private getDefaultFeatures(tier: SubscriptionTier) {
    const baseFeatures = [
      { featureKey: 'model_access', featureValue: tier, enabled: true },
      { featureKey: 'context_caching', featureValue: tier !== 'free' ? 'enabled' : 'disabled', enabled: tier !== 'free' },
      { featureKey: 'thinking_mode', featureValue: tier === 'premium' ? 'advanced' : tier === 'basic' ? 'basic' : 'disabled', enabled: tier !== 'free' },
      { featureKey: 'structured_output', featureValue: tier !== 'free' ? 'enabled' : 'disabled', enabled: tier !== 'free' },
      { featureKey: 'function_calling', featureValue: tier !== 'free' ? 'enabled' : 'disabled', enabled: tier !== 'free' }
    ];

    switch (tier) {
      case SUBSCRIPTION_TIERS.PREMIUM:
        return [
          ...baseFeatures,
          { featureKey: 'priority_support', featureValue: 'enabled', enabled: true },
          { featureKey: 'advanced_analytics', featureValue: 'enabled', enabled: true },
          { featureKey: 'custom_models', featureValue: 'enabled', enabled: true }
        ];
      case SUBSCRIPTION_TIERS.BASIC:
        return [
          ...baseFeatures,
          { featureKey: 'standard_support', featureValue: 'enabled', enabled: true },
          { featureKey: 'basic_analytics', featureValue: 'enabled', enabled: true }
        ];
      case SUBSCRIPTION_TIERS.FREE:
      default:
        return baseFeatures;
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(userId: string, immediately: boolean = false): Promise<UserSubscription> {
    const updateData: UpdateSubscriptionData = immediately
      ? { status: 'cancelled' }
      : { cancelAtPeriodEnd: true };

    return this.updateSubscription(userId, updateData);
  }

  /**
   * Reactivate subscription
   */
  async reactivateSubscription(userId: string): Promise<UserSubscription> {
    return this.updateSubscription(userId, {
      status: 'active',
      cancelAtPeriodEnd: false
    });
  }
}

export const subscriptionService = new SubscriptionService();
export default subscriptionService;
