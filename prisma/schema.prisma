// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("POSTGRES_PRISMA_URL") // uses connection pooling
  directUrl = env("POSTGRES_URL_NON_POOLING") // uses a direct connection
}

model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  conversations    Conversation[]
  messages         Message[]
  subscription     UserSubscription?
  modelUsage       ModelUsage[]

  @@map("users")
}

model Conversation {
  id          String   @id @default(cuid())
  title       String?
  userId      String?
  isArchived  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user         User?          @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages     Message[]
  modelUsage   ModelUsage[]
  contextCache ContextCache[]

  @@map("conversations")
}

model Message {
  id             String   @id @default(cuid())
  content        String
  role           String   // 'user' | 'assistant' | 'system'
  conversationId String
  userId         String?
  metadata       Json?    // For storing additional data like model, tokens, etc.
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user         User?        @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("messages")
}

model ApiUsage {
  id          String   @id @default(cuid())
  userId      String?
  model       String
  tokens      Int
  cost        Float?
  endpoint    String
  status      String   // 'success' | 'error'
  errorCode   String?
  errorMessage String?
  createdAt   DateTime @default(now())

  @@map("api_usage")
}

model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}

// GLM-4.5 Multi-Model Integration Schema Extensions

model UserSubscription {
  id                String   @id @default(cuid())
  userId            String   @unique
  tier              String   // 'free', 'basic', 'premium'
  status            String   // 'active', 'inactive', 'expired', 'cancelled'
  stripeCustomerId  String?  @unique
  stripeSubscriptionId String? @unique
  currentPeriodStart DateTime?
  currentPeriodEnd   DateTime?
  cancelAtPeriodEnd  Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  features          SubscriptionFeature[]
  usage             SubscriptionUsage[]

  @@map("user_subscriptions")
}

model SubscriptionFeature {
  id             String   @id @default(cuid())
  subscriptionId String
  featureKey     String   // 'model_access', 'context_caching', 'thinking_mode', etc.
  featureValue   String   // JSON string for complex values
  enabled        Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  subscription   UserSubscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@unique([subscriptionId, featureKey])
  @@map("subscription_features")
}

model SubscriptionUsage {
  id             String   @id @default(cuid())
  subscriptionId String
  period         String   // 'daily', 'monthly', 'yearly'
  periodStart    DateTime
  periodEnd      DateTime
  requestCount   Int      @default(0)
  tokenCount     Int      @default(0)
  cost           Float    @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  subscription   UserSubscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@unique([subscriptionId, period, periodStart])
  @@map("subscription_usage")
}

model ModelUsage {
  id             String   @id @default(cuid())
  userId         String?
  conversationId String?
  modelId        String   // 'glm-4.5', 'glm-4.5-air', 'glm-4.5-x', 'glm-4.5-airx', 'glm-4.5-flash'
  requestType    String   // 'chat', 'thinking', 'structured', 'function_call'
  inputTokens    Int      @default(0)
  outputTokens   Int      @default(0)
  totalTokens    Int      @default(0)
  cost           Float    @default(0)
  responseTime   Int?     // milliseconds
  success        Boolean  @default(true)
  errorCode      String?
  errorMessage   String?
  metadata       Json?    // Additional model-specific data
  createdAt      DateTime @default(now())

  // Relations
  user           User?         @relation(fields: [userId], references: [id], onDelete: SetNull)
  conversation   Conversation? @relation(fields: [conversationId], references: [id], onDelete: SetNull)

  @@index([userId, createdAt])
  @@index([modelId, createdAt])
  @@index([conversationId, createdAt])
  @@map("model_usage")
}

model ModelUsageAnalytics {
  id             String   @id @default(cuid())
  modelId        String
  period         String   // 'hourly', 'daily', 'weekly', 'monthly'
  periodStart    DateTime
  periodEnd      DateTime
  totalRequests  Int      @default(0)
  successfulRequests Int  @default(0)
  failedRequests Int      @default(0)
  totalTokens    Int      @default(0)
  totalCost      Float    @default(0)
  avgResponseTime Float?  // milliseconds
  uniqueUsers    Int      @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([modelId, period, periodStart])
  @@index([modelId, periodStart])
  @@map("model_usage_analytics")
}

model ContextCache {
  id             String   @id @default(cuid())
  conversationId String
  cacheKey       String   @unique
  contextData    String   // Compressed conversation context
  tokenCount     Int      @default(0)
  compressionRatio Float? // Original size / compressed size
  ttl            DateTime // Time to live
  hitCount       Int      @default(0)
  lastAccessed   DateTime @default(now())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
  @@index([ttl])
  @@index([lastAccessed])
  @@map("context_cache")
}